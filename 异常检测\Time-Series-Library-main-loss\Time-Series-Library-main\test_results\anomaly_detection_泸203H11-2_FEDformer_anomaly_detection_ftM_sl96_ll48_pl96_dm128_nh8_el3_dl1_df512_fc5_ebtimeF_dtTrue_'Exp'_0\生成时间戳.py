import pandas as pd
import os
import glob
import shutil
import sys
import argparse
from sklearn.metrics import precision_score, accuracy_score, recall_score
from datetime import timedelta, datetime

# 用户可配置参数
# ====================================================================================

# 保存方式：1 - 将结果保存到子文件夹中；2 - 将所有结果合并到一个文件中
SAVE_MODE = 1

# 结果文件所在目录（留空表示使用当前目录）
# 例如：RESULT_DIR = "D:/PyCharm/kazuan/卡钻代码label/测试结果"
#RESULT_DIR = r"D:\0temp\算法\异常检测\Time-Series-Library-main-loss\Time-Series-Library-main\test_results\anomaly_detection_泸203H11-2_FEDformer_anomaly_detection_ftM_sl96_ll48_pl96_dm128_nh8_el3_dl1_df512_fc5_ebtimeF_dtTrue_'Exp'_0"  
RESULT_DIR = ""
# ====================================================================================

def analyze_result_file(csv_file):
    """分析单个结果文件并返回分析结果文本"""
    file_name = os.path.basename(csv_file)
    result_text = f"分析文件: {file_name}\n"
    result_text += "=" * 50 + "\n\n"
    
    # 读取数据
    try:
        df = pd.read_csv(csv_file)
    except Exception as e:
        print(f"读取文件出错: {e}")
        print(f"文件路径: {csv_file}")
        raise
    
    # 检查文件是否包含真实标签列
    has_true_label = 'True_Label' in df.columns
    
    # 只有在有真实标签的情况下才计算精确率、准确率、召回率
    if has_true_label:
        y_true = df['True_Label']  # 真实标签
        y_pred = df['Predicted_Anomaly']  # 预测值

        precision = precision_score(y_true, y_pred, zero_division=0)
        accuracy = accuracy_score(y_true, y_pred)
        recall = recall_score(y_true, y_pred, zero_division=0)

        result_text += f"精确率: {precision:.2f}\n"
        result_text += f"准确率: {accuracy:.2f}\n"
        result_text += f"召回率: {recall:.2f}\n\n"
    else:
        result_text += "注意: 此文件为无标签模式生成，无法计算评估指标\n\n"

    # 打印出预测值连续为1的时间段
    continuous_ones = find_continuous_ones(df)
    result_text += "连续预测为异常的时间段：\n"
    if continuous_ones:
        for segment in continuous_ones:
            result_text += f"开始时间: {segment['start_time']}, 结束时间: {segment['end_time']}, " \
                  f"连续1的个数: {segment['length']}, 持续时间: {segment['duration_min']:.2f} 分钟\n"
    else:
        result_text += "未检测到异常\n"
    
    result_text += "\n"

    # 打印高风险预警时段 (连续2分钟及以上预测为1的时间段)
    high_risk_periods = high_risk_warning(df)
    result_text += "高风险预警时段 (持续2分钟以上)：\n"
    if high_risk_periods:
        for risk in high_risk_periods:
            result_text += f"{risk['start_time'].strftime('%Y-%m-%d %H:%M')}到{risk['end_time'].strftime('%H:%M')}, " \
                  f"持续 {risk['risk_duration_min']:.0f} 分钟\n"
    else:
        result_text += "未检测到高风险预警\n"
    
    result_text += "\n" + "=" * 50 + "\n\n"    
    return result_text, high_risk_periods

def extract_well_name(file_name):
    """从文件名中提取井名"""
    # 尝试提取井名，如果文件名以数字+井结尾或包含井字
    import re
    well_name = ""
    
    # 尝试匹配形如 "xxx泸203H11-5xxx" 的模式
    pattern1 = r'([泸宁自足][\d]+[A-Z][\d]+[-]?[\d]*)'  # 匹配类似泸203H11-5的井名
    match = re.search(pattern1, file_name)
    if match:
        well_name = match.group(1)
        return well_name
    
    # 尝试匹配形如 "xxx24井xxx" 的模式
    pattern2 = r'(\d+井)'  # 匹配类似24井的井名
    match = re.search(pattern2, file_name)
    if match:
        well_name = match.group(1)
        return well_name
    
    # 如果都没匹配到，返回文件名前部分作为井名
    return os.path.splitext(file_name)[0].split('_')[0]

def find_continuous_ones(df):
    """打印出预测值连续为1的时间段"""
    df['timestamp'] = pd.to_datetime(df['Timestamp'])  # 转换时间戳格式
    continuous_ones = []
    start_idx = None

    for i in range(len(df)):
        # 找到新的连续1的开始
        if df['Predicted_Anomaly'].iloc[i] == 1 and (i == 0 or df['Predicted_Anomaly'].iloc[i-1] == 0):
            start_idx = i
        
        # 找到连续1的结束
        if (i > 0 and df['Predicted_Anomaly'].iloc[i] == 0 and df['Predicted_Anomaly'].iloc[i-1] == 1) or \
           (df['Predicted_Anomaly'].iloc[i] == 1 and i == len(df) - 1):
            # 处理最后一个记录是1的情况
            end_idx = i if df['Predicted_Anomaly'].iloc[i] == 0 else i
            
            if start_idx is not None:
                duration = (df['timestamp'].iloc[end_idx-1] - df['timestamp'].iloc[start_idx]).total_seconds() / 60  # 持续时间以分钟计算
                continuous_ones.append({
                    'start_time': df['timestamp'].iloc[start_idx],
                    'end_time': df['timestamp'].iloc[end_idx-1],
                    'length': end_idx - start_idx,
                    'duration_min': duration
                })
                start_idx = None

    return continuous_ones

def high_risk_warning(df, time_window_minutes=2):
    """打印出高风险预警时间（连续2分钟预测为1）"""
    df['timestamp'] = pd.to_datetime(df['Timestamp'])
    high_risk_periods = []
    start_idx = None

    for i in range(len(df)):
        # 如果预测值为1，并且还在同一个高风险段内
        if df['Predicted_Anomaly'].iloc[i] == 1:
            if start_idx is None:  # 找到一个新的高风险段开始
                start_idx = i
        else:
            if start_idx is not None:  # 结束一个高风险段
                end_idx = i - 1
                duration = (df['timestamp'].iloc[end_idx] - df['timestamp'].iloc[start_idx]).total_seconds() / 60
                if duration >= time_window_minutes:  # 如果持续时间超过指定分钟
                    high_risk_periods.append({
                        'start_time': df['timestamp'].iloc[start_idx],
                        'end_time': df['timestamp'].iloc[end_idx],
                        'risk_duration_min': duration
                    })
                start_idx = None

    # 处理最后一个高风险段
    if start_idx is not None:
        end_idx = len(df) - 1
        duration = (df['timestamp'].iloc[end_idx] - df['timestamp'].iloc[start_idx]).total_seconds() / 60
        if duration >= time_window_minutes:
            high_risk_periods.append({
                'start_time': df['timestamp'].iloc[start_idx],
                'end_time': df['timestamp'].iloc[end_idx],
                'risk_duration_min': duration
            })

    return high_risk_periods

def find_csv_files():
    """根据设置查找CSV结果文件"""
    # 如果设置了结果目录，则使用该目录
    if RESULT_DIR and os.path.exists(RESULT_DIR):
        search_path = os.path.join(RESULT_DIR, "anomaly_results_*.csv")
        result_files = glob.glob(search_path)
        
        if not result_files:
            print(f"在指定目录 '{RESULT_DIR}' 中未找到结果文件")
            
            # 尝试递归搜索子目录
            print(f"尝试在子目录中查找...")
            result_files = []
            for root, dirs, files in os.walk(RESULT_DIR):
                for file in files:
                    if file.startswith("anomaly_results_") and file.endswith(".csv"):
                        result_files.append(os.path.join(root, file))
                        
            if not result_files:
                print(f"在 '{RESULT_DIR}' 及其子目录中未找到结果文件")
            else:
                print(f"在子目录中找到 {len(result_files)} 个结果文件")
                
        return result_files
    
    # 否则在当前目录查找
    result_files = glob.glob('anomaly_results_*.csv')
    
    if not result_files:
        # 尝试在脚本所在目录查找
        script_dir = os.path.dirname(os.path.abspath(__file__))
        if script_dir != os.getcwd():
            print(f"当前目录未找到结果文件，尝试在脚本所在目录查找...")
            script_dir_files = glob.glob(os.path.join(script_dir, 'anomaly_results_*.csv'))
            
            if script_dir_files:
                print(f"在脚本所在目录找到 {len(script_dir_files)} 个结果文件")
                return script_dir_files
        
        # 自动在上级目录查找
        parent_dir = os.path.abspath(os.path.join(os.getcwd(), os.pardir))
        parent_result_files = glob.glob(os.path.join(parent_dir, 'anomaly_results_*.csv'))
        
        if parent_result_files:
            print(f"在上级目录找到 {len(parent_result_files)} 个结果文件")
            return parent_result_files
        
        # 自动递归查找子目录
        print("尝试在子目录中查找...")
        for root, dirs, files in os.walk(os.getcwd()):
            for file in files:
                if file.startswith('anomaly_results_') and file.endswith('.csv'):
                    result_files.append(os.path.join(root, file))
        
        if result_files:
            print(f"在子目录中找到 {len(result_files)} 个结果文件")
            return result_files
        
        print("未找到任何结果文件！请修改脚本中的RESULT_DIR变量，指定包含结果文件的目录")
                
    return result_files

def save_to_subfolder(result_files):
    """将分析结果保存到子文件夹中"""
    # 获取结果文件所在目录
    result_dir = os.path.dirname(result_files[0]) if result_files else os.getcwd()
    
    # 创建结果子文件夹
    results_folder = os.path.join(result_dir, "分析结果_" + datetime.now().strftime("%Y%m%d_%H%M%S"))
    if not os.path.exists(results_folder):
        os.makedirs(results_folder)
    
    # 收集所有井的高风险数据，使用字典按井名组织
    well_risks = {}
    
    # 为每个文件生成分析结果并保存
    for csv_file in result_files:
        # 生成输出文件名
        file_base = os.path.splitext(os.path.basename(csv_file))[0]
        txt_file = os.path.join(results_folder, f"{file_base}_分析结果.txt")
        
        # 分析文件并保存结果
        try:
            result_text, high_risk_periods = analyze_result_file(csv_file)
            
            # 保存到txt文件
            with open(txt_file, 'w', encoding='utf-8') as f:
                f.write(result_text)
            
            # 提取井名
            well_name = extract_well_name(os.path.basename(csv_file))
            
            # 汇总该井的高风险时段
            if well_name not in well_risks:
                well_risks[well_name] = []
                
            if high_risk_periods:
                for risk in high_risk_periods:
                    risk_desc = f"{risk['start_time'].strftime('%Y-%m-%d %H:%M')}到{risk['end_time'].strftime('%H:%M')}, 持续 {risk['risk_duration_min']:.0f} 分钟"
                    well_risks[well_name].append(risk_desc)
            else:
                # 如果没有高风险时段，记录为"未检测到高风险预警"
                if len(well_risks[well_name]) == 0:  # 避免重复添加
                    well_risks[well_name].append("未检测到高风险预警")
            
            print(f"已生成分析报告: {txt_file}")
            
        except Exception as e:
            print(f"处理文件 {csv_file} 时出错: {str(e)}")
            print(f"错误类型: {type(e).__name__}")
            import traceback
            traceback.print_exc()
    
    # 保存所有井的高风险数据到CSV（合并同一井的多个高风险时段到一个单元格）
    if well_risks:
        # 将字典转换为DataFrame
        risk_data = []
        for well, risks in well_risks.items():
            risk_text = "\n".join(risks)
            risk_data.append({'井名': well, '高风险时段': risk_text})
            
        risk_df = pd.DataFrame(risk_data)
        risk_csv = os.path.join(results_folder, "井高风险时段汇总.csv")
        risk_df.to_csv(risk_csv, index=False, encoding='utf-8-sig')
        print(f"已保存高风险时段汇总: {risk_csv}")
    
    print(f"\n分析完成！所有报告已保存到 '{results_folder}' 文件夹")

def save_to_single_file(result_files):
    """将所有分析结果合并到一个文件中"""
    # 获取结果文件所在目录
    result_dir = os.path.dirname(result_files[0]) if result_files else os.getcwd()
    
    # 创建输出文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = os.path.join(result_dir, f"卡钻分析结果汇总_{timestamp}.txt")
    all_results = f"卡钻检测分析报告汇总\n生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
    
    # 收集所有井的高风险数据，使用字典按井名组织
    well_risks = {}
    
    # 分析所有文件
    for csv_file in result_files:
        try:
            # 分析文件
            result_text, high_risk_periods = analyze_result_file(csv_file)
            all_results += result_text
            
            # 提取井名
            well_name = extract_well_name(os.path.basename(csv_file))
            
            # 汇总该井的高风险时段
            if well_name not in well_risks:
                well_risks[well_name] = []
                
            if high_risk_periods:
                for risk in high_risk_periods:
                    risk_desc = f"{risk['start_time'].strftime('%Y-%m-%d %H:%M')}到{risk['end_time'].strftime('%H:%M')}, 持续 {risk['risk_duration_min']:.0f} 分钟"
                    well_risks[well_name].append(risk_desc)
            else:
                # 如果没有高风险时段，记录为"未检测到高风险预警"
                if len(well_risks[well_name]) == 0:  # 避免重复添加
                    well_risks[well_name].append("未检测到高风险预警")
                
            print(f"已分析文件: {os.path.basename(csv_file)}")
        except Exception as e:
            print(f"处理文件 {csv_file} 时出错: {str(e)}")
            print(f"错误类型: {type(e).__name__}")
            import traceback
            traceback.print_exc()
            all_results += f"分析文件: {os.path.basename(csv_file)}\n错误: {e}\n\n"
    
    # 保存到单一文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(all_results)
    
    # 保存所有井的高风险数据到CSV（合并同一井的多个高风险时段到一个单元格）
    if well_risks:
        # 将字典转换为DataFrame
        risk_data = []
        for well, risks in well_risks.items():
            risk_text = "\n".join(risks)
            risk_data.append({'井名': well, '高风险时段': risk_text})
            
        risk_df = pd.DataFrame(risk_data)
        risk_csv = os.path.join(result_dir, f"井高风险时段汇总_{timestamp}.csv")
        risk_df.to_csv(risk_csv, index=False, encoding='utf-8-sig')
        print(f"已保存高风险时段汇总: {risk_csv}")
    
    print(f"\n分析完成！所有报告已合并保存到 '{output_file}'")

def parse_args():
    parser = argparse.ArgumentParser(description='异常检测结果分析工具')
    parser.add_argument('--dir', '-d', type=str, help='指定包含结果文件的目录路径')
    parser.add_argument('--mode', '-m', type=int, choices=[1, 2], help='保存模式: 1-子文件夹, 2-单文件')
    return parser.parse_args()

def main():
    print("=" * 60)
    print("卡钻异常检测结果分析工具")
    print("=" * 60)
    
    # 显示当前设置
    if RESULT_DIR:
        print(f"结果文件目录: {RESULT_DIR}")
    else:
        print("结果文件目录: 未指定（自动查找）")
    
    print(f"保存模式: {'单独文件夹' if SAVE_MODE == 1 else '合并文件'}")
    print("=" * 60)
    
    print("\n正在查找异常检测结果文件...")
    # 自动查找CSV文件
    result_files = find_csv_files()
    
    if not result_files:
        print("\n未找到任何异常检测结果文件！请检查以下几点：")
        print("1. 确保结果文件名以'anomaly_results_'开头，并以'.csv'结尾")
        print("2. 在脚本顶部设置RESULT_DIR变量，指定包含结果文件的目录")
        input("\n按任意键退出...")
        return
    
    print(f"\n找到 {len(result_files)} 个结果文件:")
    for i, file in enumerate(result_files):
        print(f"  {i+1}. {file}")
    
    # 根据保存模式选择保存方法
    if SAVE_MODE == 1:
        print("\n使用模式1: 将结果保存到子文件夹中")
        save_to_subfolder(result_files)
    else:
        print("\n使用模式2: 将所有结果合并到一个文件中")
        save_to_single_file(result_files)
    
    print("\n分析完成！按任意键退出...")
    input()

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n程序运行出错: {str(e)}")
        print(f"错误类型: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        print("\n按任意键退出...")
        input()

