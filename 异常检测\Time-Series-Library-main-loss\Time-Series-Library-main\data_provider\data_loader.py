import os
import numpy as np
import pandas as pd
import glob
import re
import torch
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from utils.timefeatures import time_features
from data_provider.m4 import M4Dataset, M4Meta
from data_provider.uea import subsample, interpolate_missing, Normalizer
from sktime.datasets import load_from_tsfile_to_dataframe
import warnings
import datetime

warnings.filterwarnings('ignore')

# 添加格式缓存
_FORMAT_CACHE = {}
_FIRST_ERROR_PRINTED = False  # 控制只打印首次错误
_PROCESSED_TIMESTAMPS = {}  # 存储已处理过的时间戳数组

def safe_parse_datetime(datetime_array):
    """安全地将各种格式的日期时间字符串转换为数值时间戳，使用缓存提高效率"""
    global _FIRST_ERROR_PRINTED, _PROCESSED_TIMESTAMPS
    
    # 检查是否已经处理过相同的数组（基于内存地址）
    array_id = id(datetime_array)
    if array_id in _PROCESSED_TIMESTAMPS:
        return _PROCESSED_TIMESTAMPS[array_id]
    
    try:
        # 尝试直接转换
        result = datetime_array.astype('datetime64[s]').astype('float64')
        # 缓存结果
        _PROCESSED_TIMESTAMPS[array_id] = result
        return result
    except ValueError:
        # 减少输出，不再每次都打印错误
        if not _FIRST_ERROR_PRINTED:
            print("标准时间戳解析失败，使用快速解析...")
            _FIRST_ERROR_PRINTED = True
        
        # 快速检查 - 如果全部是相同格式的字符串，使用简化方法
        if len(datetime_array) > 0 and all(isinstance(dt, str) for dt in datetime_array):
            # 尝试识别常见的中文日期格式：YYYY/MM/DD HH:MM
            sample = datetime_array[0] 
            if '/' in sample and ':' in sample:
                try:
                    # 快速转换全部时间戳
                    timestamps = np.array([pd.to_datetime(dt).timestamp() for dt in datetime_array])
                    _PROCESSED_TIMESTAMPS[array_id] = timestamps
                    return timestamps
                except:
                    pass # 如果批量转换失败，回退到逐个解析
        
        # 手动快速创建数值时间戳
        timestamps = np.zeros(len(datetime_array), dtype=np.float64)
        
        # 先尝试批量转换最常见的日期格式
        try:
            if all(isinstance(dt, str) for dt in datetime_array):
                converted = pd.to_datetime(datetime_array)
                if not converted.isna().any():
                    timestamps = converted.astype('int64') // 10**9  # 纳秒转秒
                    _PROCESSED_TIMESTAMPS[array_id] = timestamps
                    return timestamps
        except:
            pass  # 失败则继续
            
        # 逐个处理
        for i, dt_str in enumerate(datetime_array):
            try:
                if isinstance(dt_str, str):
                    # 简化解析逻辑
                    if ':' in dt_str:  # 包含时间部分
                        # 补全秒数
                        if dt_str.count(':') == 1:
                            dt_str += ":00"
                    timestamps[i] = pd.to_datetime(dt_str).timestamp()
                elif isinstance(dt_str, (int, float)):
                    timestamps[i] = float(dt_str)
                elif isinstance(dt_str, (datetime.datetime, pd.Timestamp)):
                    timestamps[i] = dt_str.timestamp()
                else:
                    timestamps[i] = float(pd.to_datetime(dt_str).timestamp())
            except:
                # 转换失败直接使用索引
                timestamps[i] = float(i)
        
        # 缓存结果
        _PROCESSED_TIMESTAMPS[array_id] = timestamps
        return timestamps


# 泸203H11-5
class Ning209H83Loader(Dataset):
    def __init__(self, root_path, win_size, step=1, flag="train", test_file=None, label_file=None, no_label=False):
        self.flag = flag
        self.step = step
        self.win_size = win_size
        self.scaler = StandardScaler()
        self.no_label = no_label
        
        # 加载训练数据 (不受test_dir影响)
        try:
            # 首先尝试从当前根目录加载训练数据
            train_data_path = os.path.join(root_path, "train自201H54-3.npy")
            if not os.path.exists(train_data_path):
                # 如果找不到，则尝试从标准位置加载
                default_train_path = os.path.join("./dataset/test", "train自201H54-3.npy")
                if os.path.exists(default_train_path):
                    train_data_path = default_train_path
                    print(f"从默认位置加载训练数据: {default_train_path}")
                else:
                    raise FileNotFoundError(f"找不到训练数据，已尝试路径: {train_data_path}, {default_train_path}")
                
            train_data = np.load(train_data_path, allow_pickle=True)
            print(f"成功加载训练数据: {train_data_path}")
        except Exception as e:
            print(f"加载训练数据失败: {e}")
            # 创建一个假的训练数据，避免程序崩溃
            train_data = np.zeros((100, 13))
            print("使用空训练数据代替")
            
        train_data = train_data[:, :-1]
        self.scaler.fit(train_data)
        data = self.scaler.transform(train_data)
        
        # 使用传入的测试文件名，如果没有则使用默认值
        test_file_name = test_file if test_file else "24井实时数据.npy"
        
        # 加载测试数据，使用root_path
        try:
            test_data_path = os.path.join(root_path, test_file_name)
            test_data = np.load(test_data_path, allow_pickle=True)
            print(f"成功加载测试数据: {test_data_path}")
        except Exception as e:
            print(f"加载指定测试数据失败: {e}")
            print(f"尝试的路径: {test_data_path}")
            # 尝试在其他可能位置查找
            try:
                # 尝试在默认路径查找
                default_test_path = os.path.join("./dataset/test", test_file_name)
                test_data = np.load(default_test_path, allow_pickle=True)
                print(f"从默认路径成功加载测试数据: {default_test_path}")
            except Exception as e2:
                print(f"所有测试数据加载尝试均失败: {e2}")
                # 创建假数据避免崩溃
                test_data = np.zeros((100, 13))
                print("使用空测试数据代替")
                
        timestamp = test_data[:, -1]
        test_data = test_data[:, :-1]
        self.test = self.scaler.transform(test_data)
        self.train = data
        self.timestamp = timestamp
        
        # *** 关键优化 ***
        # 预先处理所有时间戳，避免每次窗口重复解析
        try:
            print("预处理时间戳数据...")
            self.numeric_timestamps = safe_parse_datetime(self.timestamp)
            print("时间戳预处理完成")
        except Exception as e:
            print(f"时间戳预处理失败: {e}，将在需要时处理")
            self.numeric_timestamps = None
            
        data_len = len(self.train)
        self.val = self.train[(int)(data_len * 0.8):]
        
        # 只在非无标签模式下加载标签
        if not no_label:
            label_file_name = label_file if label_file else f"{os.path.splitext(test_file_name)[0]}_label.npy"
            try:
                # 首先在root_path中查找标签文件
                label_path = os.path.join(root_path, label_file_name)
                if os.path.exists(label_path):
                    self.test_labels = np.load(label_path, allow_pickle=True)
                    print(f"标签文件已加载: {label_path}")
                else:
                    # 尝试在默认位置查找
                    default_label_path = os.path.join("./dataset/test", label_file_name)
                    if os.path.exists(default_label_path):
                        self.test_labels = np.load(default_label_path, allow_pickle=True)
                        print(f"从默认位置加载标签: {default_label_path}")
                    else:
                        raise FileNotFoundError(f"找不到标签文件: {label_path}, {default_label_path}")
            except Exception as e:
                print(f"警告: 加载标签文件失败: {e}")
                # 创建一个零标签数组作为替代
                self.test_labels = np.zeros(len(test_data))
                print("使用零标签替代")
        else:
            print("无标签模式: 不加载标签文件")
            # 创建一个零标签数组
            self.test_labels = np.zeros(len(test_data))
        
        print("测试文件:", test_file_name)
        print("test:", self.test.shape)
        print("train:", self.train.shape)

        min_length = self.win_size + 10  # 确保数据长度至少比窗口大小多一些
        if len(self.train) < min_length or len(self.val) < min_length or len(self.test) < min_length:
            raise ValueError(
                f"数据集长度不足！train:{len(self.train)}, val:{len(self.val)}, test:{len(self.test)}, 需要至少{min_length}")

    def __len__(self):
        if self.flag == "train":
            return (self.train.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'val'):
            return (self.val.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'test'):
            return (self.test.shape[0] - self.win_size) // self.step + 1
        else:
            return (self.test.shape[0] - self.win_size) // self.win_size + 1

    def __getitem__(self, index):
        index = index * self.step
        # 确保索引不会超出数据范围
        if self.flag == "train":
            if index + self.win_size > len(self.train):
                index = len(self.train) - self.win_size
            # 恢复为只返回两个值
            return np.float32(self.train[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'val'):
            # 返回三个值用于验证
            if index + self.win_size > len(self.val):
                index = len(self.val) - self.win_size
            return np.float32(self.val[index:index + self.win_size]), np.float32(
                self.test_labels[0:self.win_size]), np.zeros(self.win_size)
        elif (self.flag == 'test'):
            if index + self.win_size > len(self.test):
                index = len(self.test) - self.win_size
                
            # *** 关键优化 ***
            # 直接使用预处理的时间戳数据，避免重复解析
            if self.numeric_timestamps is not None:
                timestamps = self.numeric_timestamps[index:index + self.win_size]
            else:
                timestamps = safe_parse_datetime(self.timestamp[index:index + self.win_size])
                
            return np.float32(self.test[index:index + self.win_size]), np.float32(
                self.test_labels[index:index + self.win_size]), timestamps
        else:
            if index // self.step * self.win_size + self.win_size > len(self.test):
                index = (len(self.test) - self.win_size) * self.step // self.win_size
            return np.float32(self.test[
                              index // self.step * self.win_size:index // self.step * self.win_size + self.win_size]), np.float32(
                self.test_labels[index // self.step * self.win_size:index // self.step * self.win_size + self.win_size])


# 自201H35-2
# class Ning209H83Loader(Dataset):
#     def __init__(self, root_path, win_size, step=1, flag="train"):
#         self.flag = flag
#         self.step = step
#         self.win_size = win_size
#         self.scaler = StandardScaler()
#         train_data = np.load(os.path.join(root_path, "train2201自201H35-2.npy"), allow_pickle=True)
#         train_data = train_data[:, :-1]
#         self.scaler.fit(train_data)
#         data = self.scaler.transform(train_data)
#         test_data = np.load(os.path.join(root_path, "test2203自201H35-2.npy"), allow_pickle=True)
#         timestamp = test_data[:, -1]
#         test_data = test_data[:, :-1]
#         self.test = self.scaler.transform(test_data)
#         self.train = data
#         self.timestamp = timestamp
#         data_len = len(self.train)
#         self.val = self.train[(int)(data_len * 0.8):]
#         self.test_labels = np.load(os.path.join(root_path, "testlabel自201H35-2 - 副本.npy"), allow_pickle=True)
#         print("test:", self.test.shape)
#         print("train:", self.train.shape)
#
#     def __len__(self):
#         if self.flag == "train":
#             return (self.train.shape[0] - self.win_size) // self.step + 1
#         elif (self.flag == 'val'):
#             return (self.val.shape[0] - self.win_size) // self.step + 1
#         elif (self.flag == 'test'):
#             return (self.test.shape[0] - self.win_size) // self.step + 1
#         else:
#             return (self.test.shape[0] - self.win_size) // self.win_size + 1
#
#     def __getitem__(self, index):
#         index = index * self.step
#         if self.flag == "train":
#             return np.float32(self.train[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
#         elif (self.flag == 'val'):
#             return np.float32(self.val[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size]), self.timestamp[index:index + self.win_size].astype('datetime64[s]').astype('float64')
#         elif (self.flag == 'test'):
#             return np.float32(self.test[index:index + self.win_size]), np.float32(
#                 self.test_labels[index:index + self.win_size]), self.timestamp[index:index + self.win_size].astype('datetime64[s]').astype('float64')
#         else:
#             return np.float32(self.test[
#                               index // self.step * self.win_size:index // self.step * self.win_size + self.win_size]), np.float32(
#                 self.test_labels[index // self.step * self.win_size:index // self.step * self.win_size + self.win_size])


class Dataset_ETT_hour(Dataset):
    def __init__(self, root_path, flag='train', size=None,
                 features='S', data_path='ETTh1.csv',
                 target='OT', scale=True, timeenc=0, freq='h', seasonal_patterns=None):
        # size [seq_len, label_len, pred_len]
        # info
        if size == None:
            self.seq_len = 24 * 4 * 4
            self.label_len = 24 * 4
            self.pred_len = 24 * 4
        else:
            self.seq_len = size[0]
            self.label_len = size[1]
            self.pred_len = size[2]
        # init
        assert flag in ['train', 'test', 'val']
        type_map = {'train': 0, 'val': 1, 'test': 2}
        self.set_type = type_map[flag]

        self.features = features
        self.target = target
        self.scale = scale
        self.timeenc = timeenc
        self.freq = freq

        self.root_path = root_path
        self.data_path = data_path
        self.__read_data__()

    def __read_data__(self):
        self.scaler = StandardScaler()
        df_raw = pd.read_csv(os.path.join(self.root_path,
                                          self.data_path))

        border1s = [0, 12 * 30 * 24 - self.seq_len, 12 * 30 * 24 + 4 * 30 * 24 - self.seq_len]
        border2s = [12 * 30 * 24, 12 * 30 * 24 + 4 * 30 * 24, 12 * 30 * 24 + 8 * 30 * 24]
        border1 = border1s[self.set_type]
        border2 = border2s[self.set_type]

        if self.features == 'M' or self.features == 'MS':
            cols_data = df_raw.columns[1:]
            df_data = df_raw[cols_data]
        elif self.features == 'S':
            df_data = df_raw[[self.target]]

        if self.scale:
            train_data = df_data[border1s[0]:border2s[0]]
            self.scaler.fit(train_data.values)
            data = self.scaler.transform(df_data.values)
        else:
            data = df_data.values

        df_stamp = df_raw[['date']][border1:border2]
        df_stamp['date'] = pd.to_datetime(df_stamp.date)
        if self.timeenc == 0:
            df_stamp['month'] = df_stamp.date.apply(lambda row: row.month, 1)
            df_stamp['day'] = df_stamp.date.apply(lambda row: row.day, 1)
            df_stamp['weekday'] = df_stamp.date.apply(lambda row: row.weekday(), 1)
            df_stamp['hour'] = df_stamp.date.apply(lambda row: row.hour, 1)
            data_stamp = df_stamp.drop(['date'], 1).values
        elif self.timeenc == 1:
            data_stamp = time_features(pd.to_datetime(df_stamp['date'].values), freq=self.freq)
            data_stamp = data_stamp.transpose(1, 0)

        self.data_x = data[border1:border2]
        self.data_y = data[border1:border2]
        self.data_stamp = data_stamp

    def __getitem__(self, index):
        s_begin = index
        s_end = s_begin + self.seq_len
        r_begin = s_end - self.label_len
        r_end = r_begin + self.label_len + self.pred_len

        seq_x = self.data_x[s_begin:s_end]
        seq_y = self.data_y[r_begin:r_end]
        seq_x_mark = self.data_stamp[s_begin:s_end]
        seq_y_mark = self.data_stamp[r_begin:r_end]

        return seq_x, seq_y, seq_x_mark, seq_y_mark

    def __len__(self):
        return len(self.data_x) - self.seq_len - self.pred_len + 1

    def inverse_transform(self, data):
        return self.scaler.inverse_transform(data)


class Dataset_ETT_minute(Dataset):
    def __init__(self, root_path, flag='train', size=None,
                 features='S', data_path='ETTm1.csv',
                 target='OT', scale=True, timeenc=0, freq='t', seasonal_patterns=None):
        # size [seq_len, label_len, pred_len]
        # info
        if size == None:
            self.seq_len = 24 * 4 * 4
            self.label_len = 24 * 4
            self.pred_len = 24 * 4
        else:
            self.seq_len = size[0]
            self.label_len = size[1]
            self.pred_len = size[2]
        # init
        assert flag in ['train', 'test', 'val']
        type_map = {'train': 0, 'val': 1, 'test': 2}
        self.set_type = type_map[flag]

        self.features = features
        self.target = target
        self.scale = scale
        self.timeenc = timeenc
        self.freq = freq

        self.root_path = root_path
        self.data_path = data_path
        self.__read_data__()

    def __read_data__(self):
        self.scaler = StandardScaler()
        df_raw = pd.read_csv(os.path.join(self.root_path,
                                          self.data_path))

        border1s = [0, 12 * 30 * 24 * 4 - self.seq_len, 12 * 30 * 24 * 4 + 4 * 30 * 24 * 4 - self.seq_len]
        border2s = [12 * 30 * 24 * 4, 12 * 30 * 24 * 4 + 4 * 30 * 24 * 4, 12 * 30 * 24 * 4 + 8 * 30 * 24 * 4]
        border1 = border1s[self.set_type]
        border2 = border2s[self.set_type]

        if self.features == 'M' or self.features == 'MS':
            cols_data = df_raw.columns[1:]
            df_data = df_raw[cols_data]
        elif self.features == 'S':
            df_data = df_raw[[self.target]]

        if self.scale:
            train_data = df_data[border1s[0]:border2s[0]]
            self.scaler.fit(train_data.values)
            data = self.scaler.transform(df_data.values)
        else:
            data = df_data.values

        df_stamp = df_raw[['date']][border1:border2]
        df_stamp['date'] = pd.to_datetime(df_stamp.date)
        if self.timeenc == 0:
            df_stamp['month'] = df_stamp.date.apply(lambda row: row.month, 1)
            df_stamp['day'] = df_stamp.date.apply(lambda row: row.day, 1)
            df_stamp['weekday'] = df_stamp.date.apply(lambda row: row.weekday(), 1)
            df_stamp['hour'] = df_stamp.date.apply(lambda row: row.hour, 1)
            df_stamp['minute'] = df_stamp.date.apply(lambda row: row.minute, 1)
            df_stamp['minute'] = df_stamp.minute.map(lambda x: x // 15)
            data_stamp = df_stamp.drop(['date'], 1).values
        elif self.timeenc == 1:
            data_stamp = time_features(pd.to_datetime(df_stamp['date'].values), freq=self.freq)
            data_stamp = data_stamp.transpose(1, 0)

        self.data_x = data[border1:border2]
        self.data_y = data[border1:border2]
        self.data_stamp = data_stamp

    def __getitem__(self, index):
        s_begin = index
        s_end = s_begin + self.seq_len
        r_begin = s_end - self.label_len
        r_end = r_begin + self.label_len + self.pred_len

        seq_x = self.data_x[s_begin:s_end]
        seq_y = self.data_y[r_begin:r_end]
        seq_x_mark = self.data_stamp[s_begin:s_end]
        seq_y_mark = self.data_stamp[r_begin:r_end]

        return seq_x, seq_y, seq_x_mark, seq_y_mark

    def __len__(self):
        return len(self.data_x) - self.seq_len - self.pred_len + 1

    def inverse_transform(self, data):
        return self.scaler.inverse_transform(data)


class Dataset_Custom(Dataset):
    def __init__(self, root_path, flag='train', size=None,
                 features='S', data_path='ETTh1.csv',
                 target='OT', scale=True, timeenc=0, freq='h', seasonal_patterns=None):
        # size [seq_len, label_len, pred_len]
        # info
        if size == None:
            self.seq_len = 24 * 4 * 4
            self.label_len = 24 * 4
            self.pred_len = 24 * 4
        else:
            self.seq_len = size[0]
            self.label_len = size[1]
            self.pred_len = size[2]
        # init
        assert flag in ['train', 'test', 'val']
        type_map = {'train': 0, 'val': 1, 'test': 2}
        self.set_type = type_map[flag]

        self.features = features
        self.target = target
        self.scale = scale
        self.timeenc = timeenc
        self.freq = freq

        self.root_path = root_path
        self.data_path = data_path
        self.__read_data__()

    def __read_data__(self):
        self.scaler = StandardScaler()
        df_raw = pd.read_csv(os.path.join(self.root_path,
                                          self.data_path))

        '''
        df_raw.columns: ['date', ...(other features), target feature]
        '''
        cols = list(df_raw.columns)
        cols.remove(self.target)
        cols.remove('date')
        df_raw = df_raw[['date'] + cols + [self.target]]
        num_train = int(len(df_raw) * 0.7)
        num_test = int(len(df_raw) * 0.2)
        num_vali = len(df_raw) - num_train - num_test
        border1s = [0, num_train - self.seq_len, len(df_raw) - num_test - self.seq_len]
        border2s = [num_train, num_train + num_vali, len(df_raw)]
        border1 = border1s[self.set_type]
        border2 = border2s[self.set_type]

        if self.features == 'M' or self.features == 'MS':
            cols_data = df_raw.columns[1:]
            df_data = df_raw[cols_data]
        elif self.features == 'S':
            df_data = df_raw[[self.target]]

        if self.scale:
            train_data = df_data[border1s[0]:border2s[0]]
            self.scaler.fit(train_data.values)
            data = self.scaler.transform(df_data.values)
        else:
            data = df_data.values

        df_stamp = df_raw[['date']][border1:border2]
        df_stamp['date'] = pd.to_datetime(df_stamp.date)
        if self.timeenc == 0:
            df_stamp['month'] = df_stamp.date.apply(lambda row: row.month, 1)
            df_stamp['day'] = df_stamp.date.apply(lambda row: row.day, 1)
            df_stamp['weekday'] = df_stamp.date.apply(lambda row: row.weekday(), 1)
            df_stamp['hour'] = df_stamp.date.apply(lambda row: row.hour, 1)
            data_stamp = df_stamp.drop(['date'], 1).values
        elif self.timeenc == 1:
            data_stamp = time_features(pd.to_datetime(df_stamp['date'].values), freq=self.freq)
            data_stamp = data_stamp.transpose(1, 0)

        self.data_x = data[border1:border2]
        self.data_y = data[border1:border2]
        self.data_stamp = data_stamp

    def __getitem__(self, index):
        s_begin = index
        s_end = s_begin + self.seq_len
        r_begin = s_end - self.label_len
        r_end = r_begin + self.label_len + self.pred_len

        seq_x = self.data_x[s_begin:s_end]
        seq_y = self.data_y[r_begin:r_end]
        seq_x_mark = self.data_stamp[s_begin:s_end]
        seq_y_mark = self.data_stamp[r_begin:r_end]

        return seq_x, seq_y, seq_x_mark, seq_y_mark

    def __len__(self):
        return len(self.data_x) - self.seq_len - self.pred_len + 1

    def inverse_transform(self, data):
        return self.scaler.inverse_transform(data)


class Dataset_M4(Dataset):
    def __init__(self, root_path, flag='pred', size=None,
                 features='S', data_path='ETTh1.csv',
                 target='OT', scale=False, inverse=False, timeenc=0, freq='15min',
                 seasonal_patterns='Yearly'):
        # size [seq_len, label_len, pred_len]
        # init
        self.features = features
        self.target = target
        self.scale = scale
        self.inverse = inverse
        self.timeenc = timeenc
        self.root_path = root_path

        self.seq_len = size[0]
        self.label_len = size[1]
        self.pred_len = size[2]

        self.seasonal_patterns = seasonal_patterns
        self.history_size = M4Meta.history_size[seasonal_patterns]
        self.window_sampling_limit = int(self.history_size * self.pred_len)
        self.flag = flag

        self.__read_data__()

    def __read_data__(self):
        # M4Dataset.initialize()
        if self.flag == 'train':
            dataset = M4Dataset.load(training=True, dataset_file=self.root_path)
        else:
            dataset = M4Dataset.load(training=False, dataset_file=self.root_path)
        training_values = np.array(
            [v[~np.isnan(v)] for v in
             dataset.values[dataset.groups == self.seasonal_patterns]])  # split different frequencies
        self.ids = np.array([i for i in dataset.ids[dataset.groups == self.seasonal_patterns]])
        self.timeseries = [ts for ts in training_values]

    def __getitem__(self, index):
        insample = np.zeros((self.seq_len, 1))
        insample_mask = np.zeros((self.seq_len, 1))
        outsample = np.zeros((self.pred_len + self.label_len, 1))
        outsample_mask = np.zeros((self.pred_len + self.label_len, 1))  # m4 dataset

        sampled_timeseries = self.timeseries[index]
        cut_point = np.random.randint(low=max(1, len(sampled_timeseries) - self.window_sampling_limit),
                                      high=len(sampled_timeseries),
                                      size=1)[0]

        insample_window = sampled_timeseries[max(0, cut_point - self.seq_len):cut_point]
        insample[-len(insample_window):, 0] = insample_window
        insample_mask[-len(insample_window):, 0] = 1.0
        outsample_window = sampled_timeseries[
                           cut_point - self.label_len:min(len(sampled_timeseries), cut_point + self.pred_len)]
        outsample[:len(outsample_window), 0] = outsample_window
        outsample_mask[:len(outsample_window), 0] = 1.0
        return insample, outsample, insample_mask, outsample_mask

    def __len__(self):
        return len(self.timeseries)

    def inverse_transform(self, data):
        return self.scaler.inverse_transform(data)

    def last_insample_window(self):
        """
        The last window of insample size of all timeseries.
        This function does not support batching and does not reshuffle timeseries.

        :return: Last insample window of all timeseries. Shape "timeseries, insample size"
        """
        insample = np.zeros((len(self.timeseries), self.seq_len))
        insample_mask = np.zeros((len(self.timeseries), self.seq_len))
        for i, ts in enumerate(self.timeseries):
            ts_last_window = ts[-self.seq_len:]
            insample[i, -len(ts):] = ts_last_window
            insample_mask[i, -len(ts):] = 1.0
        return insample, insample_mask


class PSMSegLoader(Dataset):
    def __init__(self, root_path, win_size, step=1, flag="train"):
        self.flag = flag
        self.step = step
        self.win_size = win_size
        self.scaler = StandardScaler()
        data = pd.read_csv(os.path.join(root_path, 'train.csv'))

        data = data.values[:, 1:]
        data = np.nan_to_num(data)
        self.scaler.fit(data)
        data = self.scaler.transform(data)
        test_data = pd.read_csv(os.path.join(root_path, 'test.csv'))
        test_data = test_data.values[:, 1:]
        test_data = np.nan_to_num(test_data)
        self.test = self.scaler.transform(test_data)
        self.train = data
        data_len = len(self.train)
        self.val = self.train[(int)(data_len * 0.8):]
        self.test_labels = pd.read_csv(os.path.join(root_path, 'test_label.csv')).values[:, 1:]
        print("test:", self.test.shape)
        print("train:", self.train.shape)

    def __len__(self):
        if self.flag == "train":
            return (self.train.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'val'):
            return (self.val.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'test'):
            return (self.test.shape[0] - self.win_size) // self.step + 1
        else:
            return (self.test.shape[0] - self.win_size) // self.win_size + 1

    def __getitem__(self, index):
        index = index * self.step
        if self.flag == "train":
            return np.float32(self.train[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'val'):
            return np.float32(self.val[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'test'):
            return np.float32(self.test[index:index + self.win_size]), np.float32(
                self.test_labels[index:index + self.win_size])
        else:
            return np.float32(self.test[
                              index // self.step * self.win_size:index // self.step * self.win_size + self.win_size]), np.float32(
                self.test_labels[index // self.step * self.win_size:index // self.step * self.win_size + self.win_size])


# class MSLSegLoader(Dataset):
#     def __init__(self, root_path, win_size, step=1, flag="train"):
#         self.flag = flag
#         self.step = step
#         self.win_size = win_size
#         self.scaler = StandardScaler()
#
#         # 加载所有训练数据文件
#         train_data_files = os.listdir(os.path.join(root_path, "train"))
#         train_data_list = []
#         for file in train_data_files:
#             if file.endswith('.npy'):
#                 data = np.load(os.path.join(root_path, "train", file), allow_pickle=True)
#                 train_data_list.append(data)
#
#         # 将所有训练数据合并
#         all_train_data = np.concatenate(train_data_list, axis=0)
#
#         # 标准化所有训练数据
#         self.scaler.fit(all_train_data)
#         self.train = self.scaler.transform(all_train_data)
#
#         # 加载测试数据并标准化
#         test_data = np.load(os.path.join(root_path, "处理后_泸203H11-4-02206卡钻.npy"), allow_pickle=True)
#         self.test = self.scaler.transform(test_data)
#
#         # 加载测试标签
#         self.test_labels = np.load(os.path.join(root_path, "宁209H8-3钻井日志_test_label.npy"), allow_pickle=True)
#
#         # 数据集划分
#         data_len = len(self.train)
#         self.val = self.train[(int)(data_len * 0.8):]
#
#         # 生成时间戳（索引值）
#         self.train_timestamps = np.arange(self.train.shape[0])
#         self.val_timestamps = self.train_timestamps[(int)(data_len * 0.8):]
#         self.test_timestamps = np.arange(self.test.shape[0])
#
#         print("test:", self.test.shape)
#         print("train:", self.train.shape)
#
#     def __len__(self):
#         if self.flag == "train":
#             return (self.train.shape[0] - self.win_size) // self.step + 1
#         elif self.flag == 'val':
#             return (self.val.shape[0] - self.win_size) // self.step + 1
#         elif self.flag == 'test':
#             return (self.test.shape[0] - self.win_size) // self.step + 1
#         else:
#             return (self.test.shape[0] - self.win_size) // self.win_size + 1
#
#     def __getitem__(self, index):
#         index = index * self.step
#         if self.flag == "train":
#             return (np.float32(self.train[index:index + self.win_size]),
#                     np.float32(self.test_labels[0:self.win_size]),
#                     self.train_timestamps[index:index + self.win_size])
#         elif self.flag == 'val':
#             return (np.float32(self.val[index:index + self.win_size]),
#                     np.float32(self.test_labels[0:self.win_size]),
#                     self.val_timestamps[index:index + self.win_size])
#         elif self.flag == 'test':
#             return (np.float32(self.test[index:index + self.win_size]),
#                     np.float32(self.test_labels[index:index + self.win_size]),
#                     self.test_timestamps[index:index + self.win_size])
#         else:
#             return (np.float32(
#                 self.test[index // self.step * self.win_size:index // self.step * self.win_size + self.win_size]),
#                     np.float32(self.test_labels[
#                                index // self.step * self.win_size:index // self.step * self.win_size + self.win_size]),
#                     self.test_timestamps[
#                     index // self.step * self.win_size:index // self.step * self.win_size + self.win_size])


class MSLSegLoader(Dataset):
    def __init__(self, root_path, win_size, step=1, flag="train"):
        self.flag = flag
        self.step = step
        self.win_size = win_size
        self.scaler = StandardScaler()

        # 加载多个训练集文件
        train_files = [f for f in os.listdir(os.path.join(root_path, "train")) if f.endswith('.npy')]
        train_data = []
        for file in train_files:
            data = np.load(os.path.join(root_path, "train", file), allow_pickle=True)
            train_data.append(data)

        # 合并所有训练集数据
        self.train = np.concatenate(train_data, axis=0)

        # 标准化
        self.scaler.fit(self.train)
        self.train = self.scaler.transform(self.train)

        # 加载测试集数据
        test_data = np.load(os.path.join(root_path, "clean_宁209H8-3钻井日志_test.npy"), allow_pickle=True)
        self.test = self.scaler.transform(test_data)

        # 加载标签数据
        self.test_labels = np.load(os.path.join(root_path, "宁209H8-3钻井日志_test_label.npy"), allow_pickle=True)

        # 划分训练集和验证集
        data_len = len(self.train)
        self.val = self.train[int(data_len * 0.8):]

        # 生成时间戳（索引值）
        self.train_timestamps = np.arange(self.train.shape[0])
        self.val_timestamps = self.train_timestamps[int(data_len * 0.8):]
        self.test_timestamps = np.arange(self.test.shape[0])

        print("test:", self.test.shape)
        print("train:", self.train.shape)

    def __len__(self):
        if self.flag == "train":
            return (self.train.shape[0] - self.win_size) // self.step + 1
        elif self.flag == 'val':
            return (self.val.shape[0] - self.win_size) // self.step + 1
        elif self.flag == 'test':
            return (self.test.shape[0] - self.win_size) // self.step + 1
        else:
            return (self.test.shape[0] - self.win_size) // self.win_size + 1

    def __getitem__(self, index):
        index = index * self.step
        if self.flag == "train":
            return (np.float32(self.train[index:index + self.win_size]),
                    np.float32(self.test_labels[0:self.win_size]),
                    self.train_timestamps[index:index + self.win_size])
        elif self.flag == 'val':
            return (np.float32(self.val[index:index + self.win_size]),
                    np.float32(self.test_labels[0:self.win_size]),
                    self.val_timestamps[index:index + self.win_size])
        elif self.flag == 'test':
            return (np.float32(self.test[index:index + self.win_size]),
                    np.float32(self.test_labels[index:index + self.win_size]),
                    self.test_timestamps[index:index + self.win_size])
        else:
            return (np.float32(
                self.test[index // self.step * self.win_size:index // self.step * self.win_size + self.win_size]),
                    np.float32(self.test_labels[
                               index // self.step * self.win_size:index // self.step * self.win_size + self.win_size]),
                    self.test_timestamps[
                    index // self.step * self.win_size:index // self.step * self.win_size + self.win_size])


class SMAPSegLoader(Dataset):
    def __init__(self, root_path, win_size, step=1, flag="train"):
        self.flag = flag
        self.step = step
        self.win_size = win_size
        self.scaler = StandardScaler()
        data = np.load(os.path.join(root_path, "SMAP_train.npy"))
        self.scaler.fit(data)
        data = self.scaler.transform(data)
        test_data = np.load(os.path.join(root_path, "SMAP_test.npy"))
        self.test = self.scaler.transform(test_data)
        self.train = data
        data_len = len(self.train)
        self.val = self.train[(int)(data_len * 0.8):]
        self.test_labels = np.load(os.path.join(root_path, "SMAP_test_label.npy"))
        print("test:", self.test.shape)
        print("train:", self.train.shape)

    def __len__(self):

        if self.flag == "train":
            return (self.train.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'val'):
            return (self.val.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'test'):
            return (self.test.shape[0] - self.win_size) // self.step + 1
        else:
            return (self.test.shape[0] - self.win_size) // self.win_size + 1

    def __getitem__(self, index):
        index = index * self.step
        if self.flag == "train":
            return np.float32(self.train[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'val'):
            return np.float32(self.val[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'test'):
            return np.float32(self.test[index:index + self.win_size]), np.float32(
                self.test_labels[index:index + self.win_size])
        else:
            return np.float32(self.test[
                              index // self.step * self.win_size:index // self.step * self.win_size + self.win_size]), np.float32(
                self.test_labels[index // self.step * self.win_size:index // self.step * self.win_size + self.win_size])


class SMDSegLoader(Dataset):
    def __init__(self, root_path, win_size, step=100, flag="train"):
        self.flag = flag
        self.step = step
        self.win_size = win_size
        self.scaler = StandardScaler()
        data = np.load(os.path.join(root_path, "SMD_train.npy"))
        self.scaler.fit(data)
        data = self.scaler.transform(data)
        test_data = np.load(os.path.join(root_path, "SMD_test.npy"))
        self.test = self.scaler.transform(test_data)
        self.train = data
        data_len = len(self.train)
        self.val = self.train[(int)(data_len * 0.8):]
        self.test_labels = np.load(os.path.join(root_path, "SMD_test_label.npy"))

    def __len__(self):
        if self.flag == "train":
            return (self.train.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'val'):
            return (self.val.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'test'):
            return (self.test.shape[0] - self.win_size) // self.step + 1
        else:
            return (self.test.shape[0] - self.win_size) // self.win_size + 1

    def __getitem__(self, index):
        index = index * self.step
        if self.flag == "train":
            return np.float32(self.train[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'val'):
            return np.float32(self.val[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'test'):
            return np.float32(self.test[index:index + self.win_size]), np.float32(
                self.test_labels[index:index + self.win_size])
        else:
            return np.float32(self.test[
                              index // self.step * self.win_size:index // self.step * self.win_size + self.win_size]), np.float32(
                self.test_labels[index // self.step * self.win_size:index // self.step * self.win_size + self.win_size])


class SWATSegLoader(Dataset):
    def __init__(self, root_path, win_size, step=1, flag="train"):
        self.flag = flag
        self.step = step
        self.win_size = win_size
        self.scaler = StandardScaler()

        train_data = pd.read_csv(os.path.join(root_path, 'swat_train2.csv'))
        test_data = pd.read_csv(os.path.join(root_path, 'swat2.csv'))
        labels = test_data.values[:, -1:]
        train_data = train_data.values[:, :-1]
        test_data = test_data.values[:, :-1]

        self.scaler.fit(train_data)
        train_data = self.scaler.transform(train_data)
        test_data = self.scaler.transform(test_data)
        self.train = train_data
        self.test = test_data
        data_len = len(self.train)
        self.val = self.train[(int)(data_len * 0.8):]
        self.test_labels = labels
        print("test:", self.test.shape)
        print("train:", self.train.shape)

    def __len__(self):
        """
        Number of images in the object dataset.
        """
        if self.flag == "train":
            return (self.train.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'val'):
            return (self.val.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'test'):
            return (self.test.shape[0] - self.win_size) // self.step + 1
        else:
            return (self.test.shape[0] - self.win_size) // self.win_size + 1

    def __getitem__(self, index):
        index = index * self.step
        if self.flag == "train":
            return np.float32(self.train[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'val'):
            return np.float32(self.val[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'test'):
            return np.float32(self.test[index:index + self.win_size]), np.float32(
                self.test_labels[index:index + self.win_size])
        else:
            return np.float32(self.test[
                              index // self.step * self.win_size:index // self.step * self.win_size + self.win_size]), np.float32(
                self.test_labels[index // self.step * self.win_size:index // self.step * self.win_size + self.win_size])


class UEAloader(Dataset):
    """
    Dataset class for datasets included in:
        Time Series Classification Archive (www.timeseriesclassification.com)
    Argument:
        limit_size: float in (0, 1) for debug
    Attributes:
        all_df: (num_samples * seq_len, num_columns) dataframe indexed by integer indices, with multiple rows corresponding to the same index (sample).
            Each row is a time step; Each column contains either metadata (e.g. timestamp) or a feature.
        feature_df: (num_samples * seq_len, feat_dim) dataframe; contains the subset of columns of `all_df` which correspond to selected features
        feature_names: names of columns contained in `feature_df` (same as feature_df.columns)
        all_IDs: (num_samples,) series of IDs contained in `all_df`/`feature_df` (same as all_df.index.unique() )
        labels_df: (num_samples, num_labels) pd.DataFrame of label(s) for each sample
        max_seq_len: maximum sequence (time series) length. If None, script argument `max_seq_len` will be used.
            (Moreover, script argument overrides this attribute)
    """

    def __init__(self, root_path, file_list=None, limit_size=None, flag=None):
        self.root_path = root_path
        self.all_df, self.labels_df = self.load_all(root_path, file_list=file_list, flag=flag)
        self.all_IDs = self.all_df.index.unique()  # all sample IDs (integer indices 0 ... num_samples-1)

        if limit_size is not None:
            if limit_size > 1:
                limit_size = int(limit_size)
            else:  # interpret as proportion if in (0, 1]
                limit_size = int(limit_size * len(self.all_IDs))
            self.all_IDs = self.all_IDs[:limit_size]
            self.all_df = self.all_df.loc[self.all_IDs]

        # use all features
        self.feature_names = self.all_df.columns
        self.feature_df = self.all_df

        # pre_process
        normalizer = Normalizer()
        self.feature_df = normalizer.normalize(self.feature_df)
        print(len(self.all_IDs))

    def load_all(self, root_path, file_list=None, flag=None):
        """
        Loads datasets from csv files contained in `root_path` into a dataframe, optionally choosing from `pattern`
        Args:
            root_path: directory containing all individual .csv files
            file_list: optionally, provide a list of file paths within `root_path` to consider.
                Otherwise, entire `root_path` contents will be used.
        Returns:
            all_df: a single (possibly concatenated) dataframe with all data corresponding to specified files
            labels_df: dataframe containing label(s) for each sample
        """
        # Select paths for training and evaluation
        if file_list is None:
            data_paths = glob.glob(os.path.join(root_path, '*'))  # list of all paths
        else:
            data_paths = [os.path.join(root_path, p) for p in file_list]
        if len(data_paths) == 0:
            raise Exception('No files found using: {}'.format(os.path.join(root_path, '*')))
        if flag is not None:
            data_paths = list(filter(lambda x: re.search(flag, x), data_paths))
        input_paths = [p for p in data_paths if os.path.isfile(p) and p.endswith('.ts')]
        if len(input_paths) == 0:
            pattern = '*.ts'
            raise Exception("No .ts files found using pattern: '{}'".format(pattern))

        all_df, labels_df = self.load_single(input_paths[0])  # a single file contains dataset

        return all_df, labels_df

    def load_single(self, filepath):
        df, labels = load_from_tsfile_to_dataframe(filepath, return_separate_X_and_y=True,
                                                   replace_missing_vals_with='NaN')
        labels = pd.Series(labels, dtype="category")
        self.class_names = labels.cat.categories
        labels_df = pd.DataFrame(labels.cat.codes,
                                 dtype=np.int8)  # int8-32 gives an error when using nn.CrossEntropyLoss

        lengths = df.applymap(
            lambda x: len(x)).values  # (num_samples, num_dimensions) array containing the length of each series

        horiz_diffs = np.abs(lengths - np.expand_dims(lengths[:, 0], -1))

        if np.sum(horiz_diffs) > 0:  # if any row (sample) has varying length across dimensions
            df = df.applymap(subsample)

        lengths = df.applymap(lambda x: len(x)).values
        vert_diffs = np.abs(lengths - np.expand_dims(lengths[0, :], 0))
        if np.sum(vert_diffs) > 0:  # if any column (dimension) has varying length across samples
            self.max_seq_len = int(np.max(lengths[:, 0]))
        else:
            self.max_seq_len = lengths[0, 0]

        # First create a (seq_len, feat_dim) dataframe for each sample, indexed by a single integer ("ID" of the sample)
        # Then concatenate into a (num_samples * seq_len, feat_dim) dataframe, with multiple rows corresponding to the
        # sample index (i.e. the same scheme as all datasets in this project)

        df = pd.concat((pd.DataFrame({col: df.loc[row, col] for col in df.columns}).reset_index(drop=True).set_index(
            pd.Series(lengths[row, 0] * [row])) for row in range(df.shape[0])), axis=0)

        # Replace NaN values
        grp = df.groupby(by=df.index)
        df = grp.transform(interpolate_missing)

        return df, labels_df

    def instance_norm(self, case):
        if self.root_path.count('EthanolConcentration') > 0:  # special process for numerical stability
            mean = case.mean(0, keepdim=True)
            case = case - mean
            stdev = torch.sqrt(torch.var(case, dim=1, keepdim=True, unbiased=False) + 1e-5)
            case /= stdev
            return case
        else:
            return case

    def __getitem__(self, ind):
        return self.instance_norm(torch.from_numpy(self.feature_df.loc[self.all_IDs[ind]].values)), \
            torch.from_numpy(self.labels_df.loc[self.all_IDs[ind]].values)

    def __len__(self):
        return len(self.all_IDs)
