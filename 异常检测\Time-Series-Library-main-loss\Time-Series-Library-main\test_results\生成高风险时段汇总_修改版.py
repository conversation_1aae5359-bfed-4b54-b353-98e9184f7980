import pandas as pd
import os
import re
from datetime import datetime, timedelta
import glob

def extract_well_info(filename):
    """从文件名中提取井名和卡钻时间"""
    # 提取井名
    # 常见井名模式，例如"泸203H11-2"、"自201H35-3"
    pattern = r'([泸宁自足长黄阳][\d]+[A-Z][\d]+[-]?[\d]*)'
    match = re.search(pattern, filename)
    well_name = match.group(1) if match else "未知井名"
    
    # 检查井名和cleaned之间是否有卡钻时间信息
    # 例如：自201H35-3_2401231315_cleaned... 中的2401231315
    parts = filename.split('_')
    stuck_time = "未卡钻"
    
    if len(parts) > 1 and "cleaned" in filename:
        # 查找井名在哪个位置
        well_index = -1
        for i, part in enumerate(parts):
            if well_name in part:
                well_index = i
                break
        
        # 查找cleaned在哪个位置
        cleaned_index = -1
        for i, part in enumerate(parts):
            if "cleaned" in part:
                cleaned_index = i
                break
        
        # 如果井名和cleaned之间有内容，可能是卡钻时间
        if well_index != -1 and cleaned_index != -1 and cleaned_index > well_index + 1:
            potential_time = parts[well_index + 1]
            # 检查是否符合日期格式(常见格式为YYMMDDHHMM)
            if re.match(r'^\d{10}$', potential_time):
                # 转换为更友好的格式
                stuck_time = potential_time
    
    return well_name, stuck_time

def extract_timestamp_from_filename(filename):
    """从文件名中提取时间戳"""
    # 寻找格式为YYYY-MM-DD_HH-MM-SS的时间戳
    pattern = r'(\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2})'
    match = re.search(pattern, filename)
    if match:
        time_str = match.group(1)
        try:
            # 转换为datetime对象
            timestamp = datetime.strptime(time_str, '%Y-%m-%d_%H-%M-%S')
            return timestamp
        except:
            pass
    return None

def parse_stuck_time(stuck_time_str):
    """将卡钻时间字符串解析为datetime对象"""
    if stuck_time_str == "未卡钻":
        return None
    
    # 尝试解析YYMMDDHHMM格式
    if re.match(r'^\d{10}$', stuck_time_str):
        try:
            # 提取年月日时分
            year = int("20" + stuck_time_str[0:2])  # 假设是21世纪
            month = int(stuck_time_str[2:4])
            day = int(stuck_time_str[4:6])
            hour = int(stuck_time_str[6:8])
            minute = int(stuck_time_str[8:10])
            
            # 创建datetime对象
            return datetime(year, month, day, hour, minute)
        except Exception as e:
            print(f"解析卡钻时间 {stuck_time_str} 失败: {e}")
    
    return None

def analyze_risk_periods(files_data, stuck_time=None):
    """分析文件风险数据，识别连续高风险时段，过滤掉卡钻时间后的预警"""
    if not files_data:
        return []
    
    # 解析卡钻时间
    stuck_datetime = parse_stuck_time(stuck_time) if stuck_time else None
    
    # 按时间戳排序
    files_data.sort(key=lambda x: x['timestamp'])
    
    # 检测连续1的时段
    risk_periods = []
    start_idx = None
    
    for i, data in enumerate(files_data):
        # 如果当前记录为高风险
        if data['risk'] > 0:
            # 如果还没有开始一个风险段，记录起始位置
            if start_idx is None:
                start_idx = i
        # 如果当前记录不是高风险，且之前有风险段
        elif start_idx is not None:
            # 结束当前风险段
            end_idx = i - 1
            # 计算持续时间
            duration = (files_data[end_idx]['timestamp'] - files_data[start_idx]['timestamp']).total_seconds() / 60
            
            # 检查是否在卡钻时间之前（如果有卡钻时间）
            is_before_stuck = True
            if stuck_datetime and files_data[start_idx]['timestamp'] >= stuck_datetime:
                is_before_stuck = False
                
            # 如果持续时间大于5分钟且在卡钻前（如果有卡钻时间），记录该高风险时段
            if duration >= 5 and is_before_stuck:
                start_time = files_data[start_idx]['timestamp']
                end_time = files_data[end_idx]['timestamp']
                risk_periods.append({
                    'start_time': start_time,
                    'end_time': end_time,
                    'duration_min': duration
                })
            
            # 重置起始位置
            start_idx = None
    
    # 处理最后一个风险段
    if start_idx is not None:
        end_idx = len(files_data) - 1
        duration = (files_data[end_idx]['timestamp'] - files_data[start_idx]['timestamp']).total_seconds() / 60
        
        # 检查是否在卡钻时间之前（如果有卡钻时间）
        is_before_stuck = True
        if stuck_datetime and files_data[start_idx]['timestamp'] >= stuck_datetime:
            is_before_stuck = False
            
        if duration >= 5 and is_before_stuck:
            start_time = files_data[start_idx]['timestamp']
            end_time = files_data[end_idx]['timestamp']
            risk_periods.append({
                'start_time': start_time,
                'end_time': end_time,
                'duration_min': duration
            })
    
    return risk_periods

def main():
    print("=" * 60)
    print("生成井高风险时段汇总（基于predictions.csv）")
    print("=" * 60)
    print("开始处理文件...")
    
    # 1. 读取predictions.csv
    predictions_file = 'predictions.csv'
    
    if not os.path.exists(predictions_file):
        print(f"错误：文件 {predictions_file} 不存在！")
        return
    
    try:
        # 尝试不同编码读取predictions.csv
        for encoding in ['gb2312', 'gbk', 'latin1', 'utf-8']:
            try:
                print(f"尝试使用 {encoding} 编码读取predictions.csv...")
                predictions_df = pd.read_csv(predictions_file, encoding=encoding)
                print(f"成功读取predictions.csv，使用编码: {encoding}")
                print(f"文件包含 {len(predictions_df)} 行数据，列名: {predictions_df.columns.tolist()}")
                break
            except Exception as e:
                print(f"{encoding} 编码读取失败: {str(e)}")
        else:
            print("无法读取predictions.csv文件，所有编码尝试均失败")
            return
        
        # 2. 确定风险列
        risk_column = None
        if 'Predicted_Label' in predictions_df.columns:
            risk_column = 'Predicted_Label'
            print(f"使用 {risk_column} 列作为风险指标")
        elif 'Risk' in predictions_df.columns:
            risk_column = 'Risk'
            print(f"使用 {risk_column} 列作为风险指标")
        else:
            print("未找到合适的风险列，无法继续处理")
            return
        
        # 3. 预处理数据
        print("正在预处理数据...")
        # 从文件名提取井名和卡钻时间
        predictions_df[['井名', '卡钻时间']] = predictions_df['Filename'].apply(
            lambda x: pd.Series(extract_well_info(x))
        )
        
        # 从文件名提取时间戳
        predictions_df['timestamp'] = predictions_df['Filename'].apply(extract_timestamp_from_filename)
        
        # 4. 按井名分组处理数据
        print("正在按井名分组处理数据...")
        results = []
        well_groups = predictions_df.groupby('井名')
        
        total_wells = len(well_groups)
        for i, (well_name, group) in enumerate(well_groups):
            if i % 10 == 0 or i == total_wells - 1:
                print(f"进度: {i+1}/{total_wells} ({(i+1)/total_wells*100:.1f}%)")
            
            # 提取卡钻时间（取组内第一个非"未卡钻"的值）
            stuck_times = group['卡钻时间'].unique()
            stuck_time = next((t for t in stuck_times if t != "未卡钻"), "未卡钻")
            
            # 整理该井的所有记录，提取需要的信息
            files_data = []
            for _, row in group.iterrows():
                if pd.notna(row['timestamp']):  # 只处理有有效时间戳的记录
                    files_data.append({
                        'filename': row['Filename'],
                        'timestamp': row['timestamp'],
                        'risk': float(row[risk_column])
                    })
            
            # 分析连续高风险时段，并过滤掉卡钻时间后的预警
            risk_periods = analyze_risk_periods(files_data, stuck_time)
            
            # 格式化高风险时段文本
            if risk_periods:
                risk_descriptions = []
                for period in risk_periods:
                    # 包含持续分钟数：MMDD HH:MM到MMDD HH:MM, 持续XX分钟
                    risk_desc = f"{period['start_time'].strftime('%m%d %H:%M')}到{period['end_time'].strftime('%m%d %H:%M')}, 持续 {int(period['duration_min'])} 分钟"
                    risk_descriptions.append(risk_desc)
                risk_text = "\n".join(risk_descriptions)
            else:
                risk_text = "无大于五分钟预警"
            
            # 添加到结果列表
            results.append({
                '井名': well_name,
                '卡钻时间': stuck_time,
                '高风险时段': risk_text
            })
        
        # 5. 创建汇总DataFrame
        print("正在创建汇总DataFrame...")
        results_df = pd.DataFrame(results)
        
        # 6. 保存结果
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"井高风险时段汇总_{timestamp}.csv"
        print(f"正在保存结果到文件: {output_file}")
        results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"已保存井高风险时段汇总文件: {output_file}")
        
    except Exception as e:
        print(f"处理文件时出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n处理完成！")
    input("按回车键退出...")

if __name__ == "__main__":
    main() 