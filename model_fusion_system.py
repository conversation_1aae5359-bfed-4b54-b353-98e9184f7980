#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型测试和融合预测系统
支持多模型加载、测试和融合预测，输出预警时间段
"""

import os
import sys
import torch
import numpy as np
import pandas as pd
from pathlib import Path
from datetime import datetime, timedelta
import json
import warnings
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
import torch.nn.functional as F

warnings.filterwarnings('ignore')

@dataclass
class ModelInfo:
    """模型信息数据类"""
    name: str
    path: str
    model_type: str  # 'fusion', 'earlysignal', 'anomaly'
    architecture: str
    seq_len: int
    d_model: int

@dataclass
class PredictionResult:
    """预测结果数据类"""
    model_name: str
    predictions: np.ndarray
    probabilities: np.ndarray = None
    confidence: float = 0.0

@dataclass
class WarningPeriod:
    """预警时间段数据类"""
    start_time: str
    end_time: str
    warning_type: str  # 'anomaly', 'earlysignal', 'fusion'
    risk_level: str    # 'low', 'medium', 'high'
    confidence: float
    details: Dict[str, Any]

class ModelFusionSystem:
    """模型融合预测系统"""

    def __init__(self, config_path: str = None):
        """
        初始化融合系统

        Args:
            config_path: 配置文件路径
        """
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.models = {}
        self.model_infos = []
        self.test_results = {}

        # 默认配置
        self.config = {
            'anomaly_threshold': 0.5,
            'earlysignal_threshold': 0.6,
            'fusion_threshold': 0.7,
            'min_warning_duration': 3,  # 最小预警持续时间（分钟）
            'fusion_weights': {
                'anomaly': 0.4,
                'earlysignal': 0.6
            },
            # 新增：特征列数量（None 表示自动根据数据推断）
            'feature_count': None
        }

        # 运行时记录：根据数据推断得到的特征列数
        self.input_features = None

        if config_path and os.path.exists(config_path):
            self.load_config(config_path)

        print(f"✓ 模型融合系统初始化完成，使用设备: {self.device}")

    def load_config(self, config_path: str):
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.config.update(config)
            print(f"✓ 配置文件加载成功: {config_path}")
        except Exception as e:
            print(f"⚠️ 配置文件加载失败: {e}，使用默认配置")

    def discover_models(self) -> List[ModelInfo]:
        """
        自动发现所有已训练的模型

        Returns:
            模型信息列表
        """
        model_infos = []

        # 搜索路径列表
        search_paths = [
            "checkpoints/",
            "前驱信号检测/checkpoints/",
            "异常检测/Time-Series-Library-main-loss/Time-Series-Library-main/checkpoints/",
            "fusion_algorithm/checkpoints/"
        ]

        for search_path in search_paths:
            if not os.path.exists(search_path):
                continue

            print(f"搜索模型路径: {search_path}")

            for root, dirs, files in os.walk(search_path):
                if 'checkpoint.pth' in files:
                    model_path = os.path.join(root, 'checkpoint.pth')
                    model_info = self._parse_model_info(model_path, root)
                    if model_info:
                        model_infos.append(model_info)

        self.model_infos = model_infos
        print(f"✓ 发现 {len(model_infos)} 个已训练模型")

        return model_infos

    def _parse_model_info(self, model_path: str, model_dir: str) -> ModelInfo:
        """
        解析模型信息

        Args:
            model_path: 模型文件路径
            model_dir: 模型目录路径

        Returns:
            模型信息对象
        """
        try:
            dir_name = os.path.basename(model_dir)

            # 解析模型类型和架构
            if 'fusion' in dir_name.lower():
                model_type = 'fusion'
                if 'anomaly' in dir_name:
                    architecture = 'FusionModel_Anomaly'
                elif 'earlysignal' in dir_name:
                    architecture = 'FusionModel_EarlySignal'
                else:
                    architecture = 'FusionModel'
            elif 'earlysignal' in dir_name.lower():
                model_type = 'earlysignal'
                if 'PatchTST' in dir_name:
                    architecture = 'PatchTST'
                elif 'DLinear' in dir_name:
                    architecture = 'DLinear'
                elif 'iTransformer' in dir_name:
                    architecture = 'iTransformer'
                elif 'Crossformer' in dir_name:
                    architecture = 'Crossformer'
                elif 'ipatch' in dir_name:
                    architecture = 'ipatch'
                elif 'EnhancedPatchTST' in dir_name:
                    architecture = 'EnhancedPatchTST'
                else:
                    architecture = 'Unknown'
            elif 'anomaly' in dir_name.lower():
                model_type = 'anomaly'
                if 'FEDformer' in dir_name:
                    architecture = 'FEDformer'
                elif 'PatchTST' in dir_name:
                    architecture = 'PatchTST'
                elif 'DLinear' in dir_name:
                    architecture = 'DLinear'
                elif 'iTransformer' in dir_name:
                    architecture = 'iTransformer'
                else:
                    architecture = 'Unknown'
            else:
                model_type = 'unknown'
                architecture = 'Unknown'

            # 解析序列长度和模型维度
            seq_len = self._extract_number(dir_name, 'sl')
            d_model = self._extract_number(dir_name, 'dm')

            return ModelInfo(
                name=dir_name,
                path=model_path,
                model_type=model_type,
                architecture=architecture,
                seq_len=seq_len if seq_len else 96,
                d_model=d_model if d_model else 128
            )

        except Exception as e:
            print(f"⚠️ 解析模型信息失败 {model_path}: {e}")
            return None

    def _extract_number(self, text: str, prefix: str) -> int:
        """从文本中提取数字"""
        import re
        pattern = f'{prefix}(\\d+)'
        match = re.search(pattern, text)
        return int(match.group(1)) if match else None

    def load_models(self, model_infos: List[ModelInfo] = None):
        """
        加载模型

        Args:
            model_infos: 要加载的模型信息列表，None则加载所有发现的模型
        """
        if model_infos is None:
            model_infos = self.model_infos

        loaded_count = 0

        for model_info in model_infos:
            try:
                # 根据模型类型创建模型实例
                model = self._create_model_instance(model_info)

                if model is None:
                    print(f"⚠️ 无法创建模型实例: {model_info.name}")
                    continue

                # 加载模型权重
                checkpoint = torch.load(model_info.path, map_location=self.device)
                model.load_state_dict(checkpoint)
                model.to(self.device)
                model.eval()

                self.models[model_info.name] = {
                    'model': model,
                    'info': model_info
                }

                loaded_count += 1
                print(f"✓ 模型加载成功: {model_info.name}")

            except Exception as e:
                print(f"❌ 模型加载失败 {model_info.name}: {e}")

        print(f"✓ 成功加载 {loaded_count}/{len(model_infos)} 个模型")

    def _create_model_instance(self, model_info: ModelInfo):
        """
        创建模型实例

        Args:
            model_info: 模型信息

        Returns:
            模型实例
        """
        try:
            if model_info.model_type == 'fusion':
                # 导入融合模型
                import sys
                import os
                import importlib.util

                fusion_path = os.path.abspath('fusion_algorithm')
                if fusion_path not in sys.path:
                    sys.path.insert(0, fusion_path)

                try:
                    # 使用importlib直接加载模块
                    spec = importlib.util.spec_from_file_location("fusion_model", os.path.join(fusion_path, "models", "fusion_model.py"))
                    fusion_module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(fusion_module)
                    FusionModel = fusion_module.FusionModel
                except ImportError as e:
                    print(f"⚠️ 导入融合模型失败: {e}")
                    return None

                # 创建配置对象
                class Config:
                    def __init__(self):
                        self.seq_len = model_info.seq_len
                        self.d_model = model_info.d_model
                        self.patch_len = 16
                        self.stride = 8
                        self.enc_in = 1  # 先占位，稍后以 self.input_features 覆盖
                        self.n_heads = 8
                        self.e_layers = 3
                        self.d_ff = 256
                        self.dropout = 0.1
                        self.activation = 'gelu'
                        self.anomaly_weight = 0.5
                        self.earlysignal_weight = 0.5
                        self.task_name = 'fusion'  # 添加缺少的task_name属性
                        self.factor = 1  # 添加缺少的factor属性
                        self.embed = 'timeF'
                        self.freq = 'h'
                        self.output_attention = False
                        self.use_amp = False
                        # 动态通道数：与输入数据特征列对齐
                        self.enc_in = getattr(self, 'enc_in', None) or getattr(self, 'c_in', None) or 1
                        self.c_out = self.enc_in
                        self.pred_len = 0

                config = Config()
                # 覆盖 enc_in/c_out 为推断到的特征数
                if hasattr(self, 'input_features') and self.input_features:
                    try:
                        config.enc_in = int(self.input_features)
                        config.c_out = int(self.input_features)
                    except Exception:
                        pass
                return FusionModel(config)

            elif model_info.model_type == 'earlysignal':
                # 导入前驱信号检测模型
                import sys
                import os
                import importlib.util

                earlysignal_path = os.path.abspath('前驱信号检测')
                if earlysignal_path not in sys.path:
                    sys.path.insert(0, earlysignal_path)

                try:
                    # 创建配置对象
                    class EarlySignalConfig:
                        def __init__(self):
                            self.task_name = 'earlysignaldet'
                            self.seq_len = model_info.seq_len
                            self.pred_len = 0
                            self.d_model = model_info.d_model
                            self.n_heads = 8
                            self.e_layers = 3
                            self.d_layers = 1
                            self.d_ff = 256
                            self.dropout = 0.1
                            self.activation = 'gelu'
                            # 动态通道数：与输入数据特征列对齐
                            self.enc_in = getattr(self, 'enc_in', None) or 1
                            self.c_out = 2  # 二分类
                            self.individual = False
                            self.patch_len = 16
                            self.stride = 8
                            self.factor = 1
                            self.embed = 'timeF'
                            self.freq = 'h'
                            # 针对部分模型要求的超参
                            self.moving_avg = getattr(self, 'moving_avg', 25)


                    config = EarlySignalConfig()
                    # 覆盖 enc_in 为推断到的特征数
                    if hasattr(self, 'input_features') and self.input_features:
                        try:
                            config.enc_in = int(self.input_features)
                        except Exception:
                            pass

                    # 根据架构类型创建模型
                    model_module = None
                    if model_info.architecture == 'PatchTST':
                        spec = importlib.util.spec_from_file_location("PatchTST", os.path.join(earlysignal_path, "models", "PatchTST.py"))
                        model_module = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(model_module)
                        return model_module.Model(config)
                    elif model_info.architecture == 'DLinear':
                        spec = importlib.util.spec_from_file_location("DLinear", os.path.join(earlysignal_path, "models", "DLinear.py"))
                        model_module = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(model_module)
                        return model_module.Model(config)
                    elif model_info.architecture == 'iTransformer':
                        spec = importlib.util.spec_from_file_location("iTransformer", os.path.join(earlysignal_path, "models", "iTransformer.py"))
                        model_module = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(model_module)
                        return model_module.Model(config)
                    elif model_info.architecture == 'Crossformer':
                        spec = importlib.util.spec_from_file_location("Crossformer", os.path.join(earlysignal_path, "models", "Crossformer.py"))
                        model_module = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(model_module)
                        return model_module.Model(config)
                    elif model_info.architecture == 'ipatch':
                        spec = importlib.util.spec_from_file_location("ipatch", os.path.join(earlysignal_path, "models", "ipatch.py"))
                        model_module = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(model_module)
                        return model_module.Model(config)
                    elif model_info.architecture == 'EnhancedPatchTST':
                        spec = importlib.util.spec_from_file_location("EnhancedPatchTST", os.path.join(earlysignal_path, "models", "EnhancedPatchTST.py"))
                        model_module = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(model_module)
                        return model_module.Model(config)
                    else:
                        print(f"⚠️ 不支持的前驱信号检测架构: {model_info.architecture}")
                        return None
                except ImportError as e:
                    print(f"⚠️ 导入前驱信号检测模型失败: {e}")
                    return None

            elif model_info.model_type == 'anomaly':
                # 导入异常检测模型
                import sys
                import os
                import importlib.util

                anomaly_path = os.path.abspath('异常检测/Time-Series-Library-main-loss/Time-Series-Library-main')
                if anomaly_path not in sys.path:
                    sys.path.insert(0, anomaly_path)

                try:
                    # 创建配置对象
                    class AnomalyConfig:
                        def __init__(self):
                            self.task_name = 'anomaly_detection'
                            self.seq_len = model_info.seq_len
                            self.pred_len = model_info.seq_len
                            self.d_model = model_info.d_model
                            self.n_heads = 8
                            self.e_layers = 2
                            self.d_layers = 1
                            self.d_ff = 32
                            self.dropout = 0.1
                            self.activation = 'gelu'
                            # 动态通道数：与输入数据特征列对齐
                            self.enc_in = getattr(self, 'enc_in', None) or 1
                            self.c_out = self.enc_in
                            self.factor = 3
                            self.embed = 'timeF'
                            self.freq = 'h'
                            self.version = 'Fourier'
                            self.mode_select = 'random'
                            self.modes = 32
                            self.L = 1
                            self.base = 'legendre'

                    config = AnomalyConfig()
                    # 覆盖 enc_in 为推断到的特征数
                    if hasattr(self, 'input_features') and self.input_features:
                        try:
                            config.enc_in = int(self.input_features)
                            config.c_out = int(self.input_features)
                        except Exception:
                            pass

                    # 根据架构类型创建模型
                    model_module = None
                    if model_info.architecture == 'FEDformer':
                        spec = importlib.util.spec_from_file_location("FEDformer", os.path.join(anomaly_path, "models", "FEDformer.py"))
                        model_module = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(model_module)
                        return model_module.Model(config)
                    elif model_info.architecture == 'PatchTST':
                        spec = importlib.util.spec_from_file_location("PatchTST", os.path.join(anomaly_path, "models", "PatchTST.py"))
                        model_module = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(model_module)
                        return model_module.Model(config)
                    elif model_info.architecture == 'DLinear':
                        spec = importlib.util.spec_from_file_location("DLinear", os.path.join(anomaly_path, "models", "DLinear.py"))
                        model_module = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(model_module)
                        return model_module.Model(config)
                    elif model_info.architecture == 'iTransformer':
                        spec = importlib.util.spec_from_file_location("iTransformer", os.path.join(anomaly_path, "models", "iTransformer.py"))
                        model_module = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(model_module)
                        return model_module.Model(config)
                    else:
                        print(f"⚠️ 不支持的异常检测架构: {model_info.architecture}")
                        return None
                except ImportError as e:
                    print(f"⚠️ 导入异常检测模型失败: {e}")
                    return None

            else:
                # 对于其他模型类型，返回None，需要具体实现
                print(f"⚠️ 暂不支持模型类型: {model_info.model_type}")
                return None

        except Exception as e:
            print(f"❌ 创建模型实例失败: {e}")
            return None

    def load_test_data(self, data_path: str = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        加载测试数据

        Args:
            data_path: 测试数据路径

        Returns:
            (test_data, test_labels) 元组
        """
        if data_path is None:
            # 使用默认测试数据路径
            test_paths = [
                "前驱信号检测/dataset/test/",
                "异常检测/Time-Series-Library-main-loss/Time-Series-Library-main/dataset/test/",
                "fusion_algorithm/data/test/"
            ]

            for path in test_paths:
                if os.path.exists(path):
                    data_path = path
                    break

        if not data_path or not os.path.exists(data_path):
            print("⚠️ 未找到测试数据，生成模拟数据")
            return self._generate_mock_test_data()

        try:
            # 加载真实测试数据
            print(f"加载测试数据: {data_path}")

            if os.path.isfile(data_path) and data_path.endswith('.csv'):
                # 单个CSV文件
                df = pd.read_csv(data_path)
                test_data, test_labels = self._process_csv_data(df)
            else:
                # 目录中的多个文件
                test_data, test_labels = self._load_directory_data(data_path)

            print(f"✓ 测试数据加载成功: {test_data.shape}")
            return test_data, test_labels

        except Exception as e:
            print(f"❌ 测试数据加载失败: {e}")
            return self._generate_mock_test_data()

    def _generate_mock_test_data(self) -> Tuple[torch.Tensor, torch.Tensor]:
        """生成模拟测试数据"""
        print("生成模拟测试数据...")

        batch_size = 100
        seq_len = 96
        # 若配置中指定了 feature_count 则使用之，否则默认 10
        features = int(self.config.get('feature_count') or 10)

        # 生成时间序列数据
        test_data = torch.randn(batch_size, seq_len, features)

        # 记录特征数
        if self.input_features is None:
            self.input_features = features

        # 生成标签（异常检测用连续值，前驱信号检测用分类标签）
        anomaly_labels = torch.rand(batch_size, seq_len, features)  # 异常分数
        earlysignal_labels = torch.randint(0, 2, (batch_size,))    # 二分类标签

        test_labels = {
            'anomaly': anomaly_labels,
            'earlysignal': earlysignal_labels
        }

        return test_data, test_labels

    def _process_csv_data(self, df: pd.DataFrame) -> Tuple[torch.Tensor, torch.Tensor]:
        """处理CSV数据（增强：自动适配特征列数，并过滤异常样本）"""
        # 1) 移除时间列
        for time_col in ['date', 'timestamp', 'time']:
            if time_col in df.columns:
                df = df.drop(time_col, axis=1)

        # 2) 仅保留数值列
        numeric_df = df.select_dtypes(include=[np.number])

        # 3) 目标特征列数（优先配置指定，否则推断；最少为1）
        target_features = self.config.get('feature_count') or numeric_df.shape[1]
        if target_features is None or target_features <= 0:
            target_features = max(1, numeric_df.shape[1])

        # 4) 对列进行对齐：
        #    - 多于 target_features：截取前 target_features 列
        #    - 少于 target_features：用0填充到 target_features 列
        if numeric_df.shape[1] >= target_features:
            numeric_df = numeric_df.iloc[:, :target_features]
        else:
            pad_cols = target_features - numeric_df.shape[1]
            for k in range(pad_cols):
                numeric_df[f'_pad_{k}'] = 0.0

        # 5) 创建滑动窗口（不足 96 行将导致无窗口，后续会在调用处汇总处理）
        seq_len = 96
        data_list = []
        for i in range(len(numeric_df) - seq_len + 1):
            window = numeric_df.iloc[i:i+seq_len].values
            data_list.append(window)

        if not data_list:
            # 返回空张量以便上层跳过
            return torch.empty(0), {'anomaly': torch.empty(0), 'earlysignal': torch.empty(0)}

        test_data = torch.FloatTensor(np.array(data_list))

        # 6) 更新全局输入特征数（仅在未设置时记录一次）
        if self.input_features is None:
            self.input_features = test_data.shape[2]

        # 7) 生成模拟标签（如后续提供 y_true，可在未来扩展）
        batch_size = test_data.shape[0]
        test_labels = {
            'anomaly': torch.rand(batch_size, seq_len, test_data.shape[2]),
            'earlysignal': torch.randint(0, 2, (batch_size,))
        }

        return test_data, test_labels

    def _load_directory_data(self, data_path: str) -> Tuple[torch.Tensor, torch.Tensor]:
        """加载目录中的数据文件（增强：过滤空样本并统一特征数）"""
        data_list = []

        for file_name in os.listdir(data_path):
            if file_name.endswith('.csv'):
                file_path = os.path.join(data_path, file_name)
                try:
                    df = pd.read_csv(file_path)
                    data, _ = self._process_csv_data(df)
                    if data is not None and data.numel() > 0:
                        data_list.append(data)
                except Exception as e:
                    print(f"⚠️ 读取文件失败，已跳过: {file_path} - {e}")

        if data_list:
            test_data = torch.cat(data_list, dim=0)
            batch_size = test_data.shape[0]
            seq_len = test_data.shape[1]
            features = test_data.shape[2]

            # 若未从任何CSV中推断 input_features，则以此处 features 为准
            if self.input_features is None:
                self.input_features = features

            test_labels = {
                'anomaly': torch.rand(batch_size, seq_len, features),
                'earlysignal': torch.randint(0, 2, (batch_size,))
            }

            return test_data, test_labels
        else:
            print("⚠️ 目录中无有效样本，回退到模拟数据")
            return self._generate_mock_test_data()

    def test_models(self, test_data: torch.Tensor, test_labels: Dict) -> Dict[str, Dict]:
        """
        测试所有模型

        Args:
            test_data: 测试数据
            test_labels: 测试标签

        Returns:
            测试结果字典
        """
        print(f"\n开始模型测试，测试数据形状: {test_data.shape}")

        test_results = {}

        for model_name, model_data in self.models.items():
            print(f"\n测试模型: {model_name}")

            try:
                model = model_data['model']
                model_info = model_data['info']

                # 执行预测
                predictions = self._predict_with_model(model, model_info, test_data)

                # 计算性能指标
                metrics = self._calculate_metrics(predictions, test_labels, model_info.model_type)

                test_results[model_name] = {
                    'predictions': predictions,
                    'metrics': metrics,
                    'model_info': model_info
                }

                print(f"✓ 模型测试完成: {model_name}")
                self._print_metrics(metrics)

            except Exception as e:
                print(f"❌ 模型测试失败 {model_name}: {e}")
                test_results[model_name] = {
                    'error': str(e),
                    'model_info': model_data['info']
                }

        self.test_results = test_results
        print(f"\n✓ 模型测试完成，共测试 {len(test_results)} 个模型")

        return test_results

    def _predict_with_model(self, model, model_info: ModelInfo, test_data: torch.Tensor) -> PredictionResult:
        """
        使用单个模型进行预测

        Args:
            model: 模型实例
            model_info: 模型信息
            test_data: 测试数据

        Returns:
            预测结果
        """
        model.eval()
        predictions = []
        probabilities = []

        with torch.no_grad():
            # 分批处理以节省内存
            batch_size = 32
            for i in range(0, test_data.shape[0], batch_size):
                batch_data = test_data[i:i+batch_size].to(self.device)

                if model_info.model_type == 'fusion':
                    # 融合模型预测
                    outputs = model(batch_data, task_mode="fusion")
                    if isinstance(outputs, dict):
                        # 取异常检测输出作为主要预测
                        batch_pred = outputs['anomaly'].cpu().numpy()
                        batch_prob = torch.sigmoid(outputs['anomaly']).cpu().numpy()
                    else:
                        batch_pred = outputs.cpu().numpy()
                        batch_prob = torch.sigmoid(outputs).cpu().numpy()

                elif model_info.model_type == 'earlysignal':
                    # 前驱信号检测预测
                    outputs = model(batch_data, None, None, None)
                    batch_prob = F.softmax(outputs, dim=1).cpu().numpy()
                    batch_pred = torch.argmax(outputs, dim=1).cpu().numpy()

                elif model_info.model_type == 'anomaly':
                    # 异常检测预测
                    outputs = model(batch_data, None, None, None)
                    batch_pred = outputs.cpu().numpy()
                    batch_prob = torch.sigmoid(outputs).cpu().numpy()

                else:
                    # 未知模型类型，使用通用预测
                    outputs = model(batch_data)
                    batch_pred = outputs.cpu().numpy()
                    batch_prob = torch.sigmoid(outputs).cpu().numpy()

                predictions.append(batch_pred)
                probabilities.append(batch_prob)

        predictions = np.concatenate(predictions, axis=0)
        probabilities = np.concatenate(probabilities, axis=0)

        # 计算置信度
        confidence = np.mean(np.max(probabilities, axis=-1)) if probabilities.ndim > 1 else np.mean(probabilities)

        return PredictionResult(
            model_name=model_info.name,
            predictions=predictions,
            probabilities=probabilities,
            confidence=float(confidence)
        )

    def _calculate_metrics(self, prediction_result: PredictionResult, test_labels: Dict, model_type: str) -> Dict:
        """
        计算性能指标

        Args:
            prediction_result: 预测结果
            test_labels: 真实标签
            model_type: 模型类型

        Returns:
            性能指标字典
        """
        metrics = {}

        try:
            if model_type == 'earlysignal':
                # 前驱信号检测指标
                y_true = test_labels['earlysignal'].numpy()
                y_pred = prediction_result.predictions
                y_prob = prediction_result.probabilities[:, 1] if prediction_result.probabilities.ndim > 1 else prediction_result.probabilities

                metrics['accuracy'] = accuracy_score(y_true, y_pred)
                metrics['precision'] = precision_score(y_true, y_pred, average='weighted', zero_division=0)
                metrics['recall'] = recall_score(y_true, y_pred, average='weighted', zero_division=0)
                metrics['f1_score'] = f1_score(y_true, y_pred, average='weighted', zero_division=0)

                if len(np.unique(y_true)) > 1:
                    metrics['auc'] = roc_auc_score(y_true, y_prob)
                else:
                    metrics['auc'] = 0.0

            elif model_type in ['anomaly', 'fusion']:
                # 异常检测指标（使用MSE和MAE）
                y_true = test_labels['anomaly'].numpy()
                y_pred = prediction_result.predictions

                # 确保维度匹配
                if y_true.shape != y_pred.shape:
                    # 如果预测结果是1D，扩展到匹配真实标签
                    if y_pred.ndim == 1 and y_true.ndim > 1:
                        y_pred = np.expand_dims(y_pred, axis=-1)
                        y_pred = np.repeat(y_pred, y_true.shape[-1], axis=-1)

                mse = np.mean((y_true - y_pred) ** 2)
                mae = np.mean(np.abs(y_true - y_pred))

                metrics['mse'] = float(mse)
                metrics['mae'] = float(mae)
                metrics['rmse'] = float(np.sqrt(mse))

                # 计算异常检测的分类指标（基于阈值）
                threshold = self.config.get('anomaly_threshold', 0.5)
                y_true_binary = (y_true > threshold).astype(int).flatten()
                y_pred_binary = (y_pred > threshold).astype(int).flatten()

                if len(np.unique(y_true_binary)) > 1:
                    metrics['accuracy'] = accuracy_score(y_true_binary, y_pred_binary)
                    metrics['precision'] = precision_score(y_true_binary, y_pred_binary, average='weighted', zero_division=0)
                    metrics['recall'] = recall_score(y_true_binary, y_pred_binary, average='weighted', zero_division=0)
                    metrics['f1_score'] = f1_score(y_true_binary, y_pred_binary, average='weighted', zero_division=0)

            metrics['confidence'] = prediction_result.confidence

        except Exception as e:
            print(f"⚠️ 指标计算失败: {e}")
            metrics = {'error': str(e), 'confidence': prediction_result.confidence}

        return metrics

    def _print_metrics(self, metrics: Dict):
        """打印性能指标"""
        for key, value in metrics.items():
            if isinstance(value, float):
                print(f"  {key}: {value:.4f}")
            else:
                print(f"  {key}: {value}")

    def fusion_predict(self, test_data: torch.Tensor, fusion_method: str = 'weighted_voting') -> PredictionResult:
        """
        融合预测

        Args:
            test_data: 测试数据
            fusion_method: 融合方法 ('weighted_voting', 'simple_average', 'confidence_weighted')

        Returns:
            融合预测结果
        """
        print(f"\n开始融合预测，方法: {fusion_method}")

        if not self.test_results:
            print("⚠️ 请先运行模型测试")
            return None

        # 收集有效的预测结果
        valid_predictions = {}
        for model_name, result in self.test_results.items():
            if 'predictions' in result and 'error' not in result:
                valid_predictions[model_name] = result['predictions']

        if not valid_predictions:
            print("❌ 没有有效的预测结果进行融合")
            return None

        print(f"使用 {len(valid_predictions)} 个模型进行融合")

        # 执行融合
        if fusion_method == 'weighted_voting':
            fusion_result = self._weighted_voting_fusion(valid_predictions, test_data)
        elif fusion_method == 'simple_average':
            fusion_result = self._simple_average_fusion(valid_predictions)
        elif fusion_method == 'confidence_weighted':
            fusion_result = self._confidence_weighted_fusion(valid_predictions)
        else:
            print(f"⚠️ 未知融合方法: {fusion_method}，使用加权投票")
            fusion_result = self._weighted_voting_fusion(valid_predictions, test_data)

        print("✓ 融合预测完成")
        return fusion_result

    def _weighted_voting_fusion(self, predictions: Dict, test_data: torch.Tensor) -> PredictionResult:
        """加权投票融合"""
        # 基于模型性能计算权重
        weights = {}
        total_weight = 0

        for model_name, pred in predictions.items():
            if model_name in self.test_results and 'metrics' in self.test_results[model_name]:
                metrics = self.test_results[model_name]['metrics']
                # 使用F1分数或置信度作为权重
                weight = metrics.get('f1_score', metrics.get('confidence', 0.5))
                weights[model_name] = max(weight, 0.1)  # 最小权重0.1
                total_weight += weights[model_name]
            else:
                weights[model_name] = 0.5
                total_weight += 0.5

        # 归一化权重
        for model_name in weights:
            weights[model_name] /= total_weight

        # 加权融合
        fusion_pred = None
        fusion_prob = None

        for model_name, pred in predictions.items():
            weight = weights[model_name]

            if fusion_pred is None:
                fusion_pred = weight * pred.predictions
                if hasattr(pred, 'probabilities') and pred.probabilities is not None:
                    fusion_prob = weight * pred.probabilities
            else:
                fusion_pred += weight * pred.predictions
                if hasattr(pred, 'probabilities') and pred.probabilities is not None:
                    fusion_prob += weight * pred.probabilities

        # 计算融合置信度
        confidence = np.mean([pred.confidence * weights[name] for name, pred in predictions.items()])

        return PredictionResult(
            model_name="WeightedVoting_Fusion",
            predictions=fusion_pred,
            probabilities=fusion_prob,
            confidence=confidence
        )

    def _simple_average_fusion(self, predictions: Dict) -> PredictionResult:
        """简单平均融合"""
        fusion_pred = None
        fusion_prob = None
        total_confidence = 0

        for model_name, pred in predictions.items():
            if fusion_pred is None:
                fusion_pred = pred.predictions / len(predictions)
                if hasattr(pred, 'probabilities') and pred.probabilities is not None:
                    fusion_prob = pred.probabilities / len(predictions)
            else:
                fusion_pred += pred.predictions / len(predictions)
                if hasattr(pred, 'probabilities') and pred.probabilities is not None:
                    fusion_prob += pred.probabilities / len(predictions)

            total_confidence += pred.confidence

        confidence = total_confidence / len(predictions)

        return PredictionResult(
            model_name="SimpleAverage_Fusion",
            predictions=fusion_pred,
            probabilities=fusion_prob,
            confidence=confidence
        )

    def _confidence_weighted_fusion(self, predictions: Dict) -> PredictionResult:
        """基于置信度的加权融合"""
        total_confidence = sum(pred.confidence for pred in predictions.values())

        if total_confidence == 0:
            return self._simple_average_fusion(predictions)

        fusion_pred = None
        fusion_prob = None

        for model_name, pred in predictions.items():
            weight = pred.confidence / total_confidence

            if fusion_pred is None:
                fusion_pred = weight * pred.predictions
                if hasattr(pred, 'probabilities') and pred.probabilities is not None:
                    fusion_prob = weight * pred.probabilities
            else:
                fusion_pred += weight * pred.predictions
                if hasattr(pred, 'probabilities') and pred.probabilities is not None:
                    fusion_prob += weight * pred.probabilities

        confidence = total_confidence / len(predictions)

        return PredictionResult(
            model_name="ConfidenceWeighted_Fusion",
            predictions=fusion_pred,
            probabilities=fusion_prob,
            confidence=confidence
        )

    def identify_warning_periods(self, fusion_result: PredictionResult,
                               timestamps: List[str] = None) -> List[WarningPeriod]:
        """
        识别预警时间段

        Args:
            fusion_result: 融合预测结果
            timestamps: 时间戳列表

        Returns:
            预警时间段列表
        """
        print("\n开始识别预警时间段...")

        if timestamps is None:
            # 生成模拟时间戳
            base_time = datetime.now()
            timestamps = [(base_time + timedelta(minutes=i)).strftime('%Y-%m-%d %H:%M:%S')
                         for i in range(len(fusion_result.predictions))]

        warning_periods = []

        # 异常检测预警
        anomaly_warnings = self._detect_anomaly_periods(
            fusion_result.predictions, timestamps, fusion_result.confidence
        )
        warning_periods.extend(anomaly_warnings)

        # 前驱信号预警
        if fusion_result.probabilities is not None:
            earlysignal_warnings = self._detect_earlysignal_periods(
                fusion_result.probabilities, timestamps, fusion_result.confidence
            )
            warning_periods.extend(earlysignal_warnings)

        # 按时间排序
        warning_periods.sort(key=lambda x: x.start_time)

        print(f"✓ 识别到 {len(warning_periods)} 个预警时间段")

        return warning_periods

    def _detect_anomaly_periods(self, predictions: np.ndarray, timestamps: List[str],
                              confidence: float) -> List[WarningPeriod]:
        """检测异常时间段"""
        threshold = self.config['anomaly_threshold']
        min_duration = self.config['min_warning_duration']

        # 将预测转换为二进制异常标记
        if predictions.ndim > 1:
            anomaly_scores = np.mean(predictions, axis=-1)
        else:
            anomaly_scores = predictions

        anomaly_binary = anomaly_scores > threshold

        warning_periods = []
        start_idx = None

        for i, is_anomaly in enumerate(anomaly_binary):
            if is_anomaly and start_idx is None:
                start_idx = i
            elif not is_anomaly and start_idx is not None:
                # 检查持续时间
                if i - start_idx >= min_duration:
                    risk_level = self._calculate_risk_level(
                        np.mean(anomaly_scores[start_idx:i])
                    )

                    warning_periods.append(WarningPeriod(
                        start_time=timestamps[start_idx],
                        end_time=timestamps[i-1],
                        warning_type='anomaly',
                        risk_level=risk_level,
                        confidence=confidence,
                        details={
                            'avg_score': float(np.mean(anomaly_scores[start_idx:i])),
                            'max_score': float(np.max(anomaly_scores[start_idx:i])),
                            'duration_minutes': i - start_idx
                        }
                    ))
                start_idx = None

        # 处理结尾的异常
        if start_idx is not None and len(anomaly_binary) - start_idx >= min_duration:
            risk_level = self._calculate_risk_level(
                np.mean(anomaly_scores[start_idx:])
            )

            warning_periods.append(WarningPeriod(
                start_time=timestamps[start_idx],
                end_time=timestamps[-1],
                warning_type='anomaly',
                risk_level=risk_level,
                confidence=confidence,
                details={
                    'avg_score': float(np.mean(anomaly_scores[start_idx:])),
                    'max_score': float(np.max(anomaly_scores[start_idx:])),
                    'duration_minutes': len(anomaly_binary) - start_idx
                }
            ))

        return warning_periods

    def _detect_earlysignal_periods(self, probabilities: np.ndarray, timestamps: List[str],
                                  confidence: float) -> List[WarningPeriod]:
        """检测前驱信号时间段"""
        threshold = self.config['earlysignal_threshold']
        min_duration = self.config['min_warning_duration']

        # 获取前驱信号概率（假设第二列是前驱信号概率）
        if probabilities.ndim > 1 and probabilities.shape[1] > 1:
            earlysignal_probs = probabilities[:, 1]
        else:
            earlysignal_probs = probabilities.flatten()

        earlysignal_binary = earlysignal_probs > threshold

        warning_periods = []
        start_idx = None

        for i, is_earlysignal in enumerate(earlysignal_binary):
            if is_earlysignal and start_idx is None:
                start_idx = i
            elif not is_earlysignal and start_idx is not None:
                # 检查持续时间
                if i - start_idx >= min_duration:
                    risk_level = self._calculate_risk_level(
                        np.mean(earlysignal_probs[start_idx:i])
                    )

                    warning_periods.append(WarningPeriod(
                        start_time=timestamps[start_idx],
                        end_time=timestamps[i-1],
                        warning_type='earlysignal',
                        risk_level=risk_level,
                        confidence=confidence,
                        details={
                            'avg_probability': float(np.mean(earlysignal_probs[start_idx:i])),
                            'max_probability': float(np.max(earlysignal_probs[start_idx:i])),
                            'duration_minutes': i - start_idx
                        }
                    ))
                start_idx = None

        # 处理结尾的前驱信号
        if start_idx is not None and len(earlysignal_binary) - start_idx >= min_duration:
            risk_level = self._calculate_risk_level(
                np.mean(earlysignal_probs[start_idx:])
            )

            warning_periods.append(WarningPeriod(
                start_time=timestamps[start_idx],
                end_time=timestamps[-1],
                warning_type='earlysignal',
                risk_level=risk_level,
                confidence=confidence,
                details={
                    'avg_probability': float(np.mean(earlysignal_probs[start_idx:])),
                    'max_probability': float(np.max(earlysignal_probs[start_idx:])),
                    'duration_minutes': len(earlysignal_binary) - start_idx
                }
            ))

        return warning_periods

    def _calculate_risk_level(self, score: float) -> str:
        """计算风险等级"""
        if score >= 0.8:
            return 'high'
        elif score >= 0.6:
            return 'medium'
        else:
            return 'low'

    def save_results(self, output_dir: str = "fusion_results"):
        """
        保存结果到文件

        Args:
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)

        # 保存测试结果
        if self.test_results:
            test_results_file = os.path.join(output_dir, "model_test_results.json")

            # 转换numpy数组为列表以便JSON序列化
            serializable_results = {}
            for model_name, result in self.test_results.items():
                serializable_result = {}
                for key, value in result.items():
                    if key == 'predictions' and hasattr(value, 'predictions'):
                        serializable_result[key] = {
                            'predictions': value.predictions.tolist() if hasattr(value.predictions, 'tolist') else str(value.predictions),
                            'confidence': value.confidence,
                            'model_name': value.model_name
                        }
                    elif key == 'model_info':
                        serializable_result[key] = {
                            'name': value.name,
                            'model_type': value.model_type,
                            'architecture': value.architecture,
                            'seq_len': value.seq_len,
                            'd_model': value.d_model
                        }
                    else:
                        serializable_result[key] = value

                serializable_results[model_name] = serializable_result

            with open(test_results_file, 'w', encoding='utf-8') as f:
                json.dump(serializable_results, f, indent=2, ensure_ascii=False)

            print(f"✓ 测试结果已保存: {test_results_file}")

    def export_warning_periods(self, warning_periods: List[WarningPeriod],
                             output_file: str = "warning_periods.csv") -> str:
        """
        导出预警时间段到CSV文件

        Args:
            warning_periods: 预警时间段列表
            output_file: 输出文件名

        Returns:
            输出文件路径
        """
        if not warning_periods:
            print("⚠️ 没有预警时间段需要导出")
            return None

        # 转换为DataFrame
        data = []
        for period in warning_periods:
            row = {
                '开始时间': period.start_time,
                '结束时间': period.end_time,
                '预警类型': period.warning_type,
                '风险等级': period.risk_level,
                '置信度': period.confidence,
                '详细信息': json.dumps(period.details, ensure_ascii=False)
            }
            data.append(row)

        df = pd.DataFrame(data)
        df.to_csv(output_file, index=False, encoding='utf-8-sig')

        print(f"✓ 预警时间段已导出: {output_file}")
        print(f"  共导出 {len(warning_periods)} 个预警时间段")

        return output_file

    def run_complete_analysis(self, test_data_path: str = None,
                            fusion_method: str = 'weighted_voting',
                            output_dir: str = "fusion_results") -> Dict:
        """
        运行完整的分析流程

        Args:
            test_data_path: 测试数据路径
            fusion_method: 融合方法
            output_dir: 输出目录

        Returns:
            分析结果字典
        """
        print("=" * 80)
        print("开始完整的模型融合分析流程")
        print("=" * 80)

        # 1. 先加载测试数据以推断特征列数
        test_data, test_labels = self.load_test_data(test_data_path)

        # 将推断到的特征数传播到配置（供后续创建模型用）
        if self.input_features is None:
            # 若仍为空，则以 config.feature_count 或默认1兜底
            self.input_features = self.config.get('feature_count') or 1

        # 2. 发现和加载模型（根据特征数动态设置 enc_in）
        model_infos = self.discover_models()
        if not model_infos:
            print("❌ 未发现任何已训练模型")
            return None

        # 在创建模型实例时读取 self.input_features
        self.load_models(model_infos)
        if not self.models:
            print("❌ 未成功加载任何模型")
            return None

        # 3. 测试所有模型
        test_results = self.test_models(test_data, test_labels)

        # 4. 融合预测
        fusion_result = self.fusion_predict(test_data, fusion_method)
        if fusion_result is None:
            print("❌ 融合预测失败")
            return None

        # 5. 识别预警时间段
        warning_periods = self.identify_warning_periods(fusion_result)

        # 6. 保存结果
        os.makedirs(output_dir, exist_ok=True)
        self.save_results(output_dir)

        warning_file = os.path.join(output_dir, "warning_periods.csv")
        self.export_warning_periods(warning_periods, warning_file)

        # 7. 生成分析报告
        analysis_results = {
            'total_models': len(model_infos),
            'loaded_models': len(self.models),
            'test_data_shape': test_data.shape,
            'fusion_method': fusion_method,
            'fusion_confidence': fusion_result.confidence,
            'warning_periods_count': len(warning_periods),
            'output_directory': output_dir
        }

        print("\n" + "=" * 80)
        print("分析完成！结果摘要：")
        print("=" * 80)
        for key, value in analysis_results.items():
            print(f"{key}: {value}")
        print("=" * 80)

        return {
            'analysis_summary': analysis_results,
            'test_results': test_results,
            'fusion_result': fusion_result,
            'warning_periods': warning_periods
        }


# 主函数示例
if __name__ == "__main__":
    # 创建融合系统
    fusion_system = ModelFusionSystem()

    # 运行完整分析
    results = fusion_system.run_complete_analysis(
        fusion_method='weighted_voting',
        output_dir='fusion_results'
    )

    if results:
        print("\n✓ 模型融合分析完成！")
        print(f"请查看输出目录: fusion_results/")
    else:
        print("\n❌ 分析失败，请检查模型和数据")
