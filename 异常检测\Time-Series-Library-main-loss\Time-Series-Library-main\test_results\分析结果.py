import pandas as pd
import os
import glob
import shutil
import sys
import argparse
from sklearn.metrics import precision_score, accuracy_score, recall_score
from datetime import timedelta, datetime

# 用户可配置参数
# ====================================================================================

# 保存方式：1 - 将结果保存到子文件夹中；2 - 将所有结果合并到一个文件中
SAVE_MODE = 1

# 结果文件所在目录（留空表示使用当前目录）
# 例如：RESULT_DIR = "D:/PyCharm/kazuan/卡钻代码label/测试结果"
RESULT_DIR = r"D:\PyCharm\kazuan\卡钻代码label\Time-Series-Library-main-loss\Time-Series-Library-main\test_results\anomaly_detection_泸203H11-2_FEDformer_anomaly_detection_ftM_sl96_ll48_pl96_dm128_nh8_el3_dl1_df512_fc5_ebtimeF_dtTrue_'Exp'_0" 

# ====================================================================================

def analyze_result_file(csv_file):
    """分析单个结果文件并返回分析结果文本"""
    file_name = os.path.basename(csv_file)
    result_text = f"分析文件: {file_name}\n"
    result_text += "=" * 50 + "\n\n"
    
    # 读取数据
    try:
        df = pd.read_csv(csv_file)
    except Exception as e:
        print(f"读取文件出错: {e}")
        print(f"文件路径: {csv_file}")
        raise
    
    # 检查文件是否包含真实标签列
    has_true_label = 'True_Label' in df.columns
    
    # 只有在有真实标签的情况下才计算精确率、准确率、召回率
    if has_true_label:
        y_true = df['True_Label']  # 真实标签
        y_pred = df['Predicted_Anomaly']  # 预测值

        precision = precision_score(y_true, y_pred, zero_division=0)
        accuracy = accuracy_score(y_true, y_pred)
        recall = recall_score(y_true, y_pred, zero_division=0)

        result_text += f"精确率: {precision:.2f}\n"
        result_text += f"准确率: {accuracy:.2f}\n"
        result_text += f"召回率: {recall:.2f}\n\n"
    else:
        result_text += "注意: 此文件为无标签模式生成，无法计算评估指标\n\n"

    # 打印出预测值连续为1的时间段
    continuous_ones = find_continuous_ones(df)
    result_text += "连续预测为异常的时间段：\n"
    if continuous_ones:
        for segment in continuous_ones:
            result_text += f"开始时间: {segment['start_time']}, 结束时间: {segment['end_time']}, " \
                  f"连续1的个数: {segment['length']}, 持续时间: {segment['duration_min']:.2f} 分钟\n"
    else:
        result_text += "未检测到异常\n"
    
    result_text += "\n"

    # 打印高风险预警时段 (连续2分钟及以上预测为1的时间段)
    high_risk_periods = high_risk_warning(df)
    result_text += "高风险预警时段 (持续2分钟以上)：\n"
    if high_risk_periods:
        for risk in high_risk_periods:
            result_text += f"{risk['start_time'].strftime('%m%d %H:%M')}到{risk['end_time'].strftime('%m%d %H:%M')}, " \
                  f"持续 {risk['risk_duration_min']:.0f} 分钟\n"
    else:
        result_text += "无大于五分钟预警\n"
    
    result_text += "\n" + "=" * 50 + "\n\n"    
    return result_text, high_risk_periods

def extract_well_info(file_name):
    """从文件名中提取井名和卡钻时间，返回(井名, 卡钻时间)元组"""
    import re
    
    # 去除文件扩展名
    file_base = os.path.splitext(file_name)[0]
    
    # 尝试匹配pattern: anomaly_results_井名_卡钻时间_时间戳
    # 例如: anomaly_results_自205H2-4_2308270427_20250505-212340
    pattern1 = r'anomaly_results_([^_]+)_(\d{10})_'
    match = re.search(pattern1, file_base)
    if match:
        well_name = match.group(1)
        stuck_time = match.group(2)
        # 格式化卡钻时间 从YYMMDDHHMM到YYYY/MM/DD HH:MM
        try:
            year = int(stuck_time[:2])
            # 如果年份是两位数，转换为四位数（假定20xx年）
            year = 2000 + year
            month = int(stuck_time[2:4])
            day = int(stuck_time[4:6])
            hour = int(stuck_time[6:8])
            minute = int(stuck_time[8:10])
            stuck_time = f"{year}/{month}/{day} {hour}:{minute}"
        except:
            # 如果格式化失败，保持原样
            pass
        return well_name, stuck_time
    
    # 尝试匹配pattern: anomaly_results_井名_时间戳 (没有卡钻时间)
    # 例如: anomaly_results_足201H8-10_20250505-212948
    pattern2 = r'anomaly_results_([^_]+)_\d+'
    match = re.search(pattern2, file_base)
    if match:
        well_name = match.group(1)
        return well_name, "不卡钻"
    
    # 如果上述模式都不匹配，尝试提取井名，卡钻时间设为"未知"
    well_name = extract_well_name(file_name)
    return well_name, "未知"

def extract_well_name(file_name):
    """从文件名中提取井名"""
    # 尝试提取井名，如果文件名以数字+井结尾或包含井字
    import re
    
    # 去除文件扩展名
    file_base = os.path.splitext(file_name)[0]
    
    # 0. 最优先：提取第一个和第二个下划线之间的内容作为井名
    parts = file_base.split('_')
    if len(parts) >= 3 and parts[0] == 'anomaly' and parts[1] == 'results' and parts[2]:
        return parts[2]
    
    # 0.1 匹配 anomaly_results_XXX 模式，取XXX作为井名
    pattern0 = r'anomaly_results_([^_\(\)]+)'
    match = re.search(pattern0, file_base)
    if match and match.group(1):
        return match.group(1)
    
    # 1. 匹配常见井名格式, 如"泸203H11-5", "阳101H43-7", "黄202H9-2"等
    pattern1 = r'([泸宁自足长黄阳][\d]+[A-Z][\d]+[-]?[\d]*)'
    match = re.search(pattern1, file_base)
    if match:
        return match.group(1)
    
    # 2. 匹配形如 "24井" 的模式
    pattern2 = r'(\d+井)'
    match = re.search(pattern2, file_base)
    if match:
        return match.group(1)
    
    # 3. 匹配形如 "JSQK1-2" 的模式 (字母+数字+连字符+数字)
    pattern3 = r'([A-Za-z]+[\d]+[-][\d]+)'
    match = re.search(pattern3, file_base)
    if match:
        return match.group(1)
    
    # 4. 特殊处理 - 匹配类似"阳101H"这种开头形式的井名
    pattern4 = r'([泸宁自足长黄阳][\d]+[A-Z])'
    match = re.search(pattern4, file_base)
    if match:
        return match.group(1)
    
    # 5. 特殊处理 - 匹配形如"长宁H3-10"这样的井名
    pattern5 = r'(长宁H\d+-\d+)'
    match = re.search(pattern5, file_base)
    if match:
        return match.group(1)
    
    # 6. 提取"results_XXX_"中的XXX作为井名，常见于anomaly_results_井名_日期.csv格式
    pattern6 = r'results_([^_]+)_'
    match = re.search(pattern6, file_base)
    if match and len(match.group(1)) > 1 and not match.group(1).isdigit():
        # 确保提取的不是纯数字
        return match.group(1)
        
    # 7. 使用更宽松的匹配来获取井名 - 在文件名中查找重要的部分
    # 通常井名出现在anomaly_results_和日期之间
    date_pattern = r'(\d{6}|\d{8}|\d{14}|\d{4}-\d{2}-\d{2}|\[\d+\]|\(\d+\))'
    
    # 分割文件名，移除日期和常见标记
    parts = re.split(date_pattern, file_base)
    cleaned_parts = [p.strip('_-() ') for p in parts if p and not re.match(r'^\s*$', p)]
    
    # 清理部分，移除常见的标记词
    ignore_terms = ['anomaly', 'results', 'result', 'csv', 'npy', 'test', 'train']
    for part in cleaned_parts:
        if not any(term in part.lower() for term in ignore_terms) and len(part) > 1:
            # 进一步清理，移除下划线和括号
            cleaned = re.sub(r'[_\(\)\[\]]', '', part).strip()
            if cleaned:
                return cleaned
    
    # 8. 最后，提取第一个非日期、非空的部分
    parts = file_base.split('_')
    for part in parts:
        if part and not re.match(r'^\d+$', part) and len(part) > 1:
            return part
    
    # 9. 如果所有方法都失败，返回整个文件名前缀
    return file_base

def find_continuous_ones(df):
    """打印出预测值连续为1的时间段"""
    df['timestamp'] = pd.to_datetime(df['Timestamp'])  # 转换时间戳格式
    continuous_ones = []
    start_idx = None

    for i in range(len(df)):
        # 找到新的连续1的开始
        if df['Predicted_Anomaly'].iloc[i] == 1 and (i == 0 or df['Predicted_Anomaly'].iloc[i-1] == 0):
            start_idx = i
        
        # 找到连续1的结束
        if (i > 0 and df['Predicted_Anomaly'].iloc[i] == 0 and df['Predicted_Anomaly'].iloc[i-1] == 1) or \
           (df['Predicted_Anomaly'].iloc[i] == 1 and i == len(df) - 1):
            # 处理最后一个记录是1的情况
            end_idx = i if df['Predicted_Anomaly'].iloc[i] == 0 else i
            
            if start_idx is not None:
                duration = (df['timestamp'].iloc[end_idx-1] - df['timestamp'].iloc[start_idx]).total_seconds() / 60  # 持续时间以分钟计算
                continuous_ones.append({
                    'start_time': df['timestamp'].iloc[start_idx],
                    'end_time': df['timestamp'].iloc[end_idx-1],
                    'length': end_idx - start_idx,
                    'duration_min': duration
                })
                start_idx = None

    return continuous_ones

def high_risk_warning(df, time_window_minutes=5):
    #打印出高风险预警时间（连续2分钟预测为1）
    df['timestamp'] = pd.to_datetime(df['Timestamp'])
    high_risk_periods = []
    start_idx = None

    for i in range(len(df)):
        # 如果预测值为1，并且还在同一个高风险段内
        if df['Predicted_Anomaly'].iloc[i] == 1:
            if start_idx is None:  # 找到一个新的高风险段开始
                start_idx = i
        else:
            if start_idx is not None:  # 结束一个高风险段
                end_idx = i - 1
                duration = (df['timestamp'].iloc[end_idx] - df['timestamp'].iloc[start_idx]).total_seconds() / 60
                if duration >= time_window_minutes:  # 如果持续时间超过指定分钟
                    high_risk_periods.append({
                        'start_time': df['timestamp'].iloc[start_idx],
                        'end_time': df['timestamp'].iloc[end_idx],
                        'risk_duration_min': duration
                    })
                start_idx = None

    # 处理最后一个高风险段
    if start_idx is not None:
        end_idx = len(df) - 1
        duration = (df['timestamp'].iloc[end_idx] - df['timestamp'].iloc[start_idx]).total_seconds() / 60
        if duration >= time_window_minutes:
            high_risk_periods.append({
                'start_time': df['timestamp'].iloc[start_idx],
                'end_time': df['timestamp'].iloc[end_idx],
                'risk_duration_min': duration
            })

    return high_risk_periods

def find_csv_files():
    """根据设置查找CSV结果文件"""
    # 如果设置了结果目录，则使用该目录
    if RESULT_DIR and os.path.exists(RESULT_DIR):
        search_path = os.path.join(RESULT_DIR, "anomaly_results_*.csv")
        result_files = glob.glob(search_path)
        
        if not result_files:
            print(f"在指定目录 '{RESULT_DIR}' 中未找到结果文件")
            
            # 尝试递归搜索子目录
            print(f"尝试在子目录中查找...")
            result_files = []
            for root, dirs, files in os.walk(RESULT_DIR):
                for file in files:
                    if file.startswith("anomaly_results_") and file.endswith(".csv"):
                        result_files.append(os.path.join(root, file))
                        
            if not result_files:
                print(f"在 '{RESULT_DIR}' 及其子目录中未找到结果文件")
            else:
                print(f"在子目录中找到 {len(result_files)} 个结果文件")
                
        return result_files
    
    # 否则在当前目录查找
    result_files = glob.glob('anomaly_results_*.csv')
    
    if not result_files:
        # 尝试在脚本所在目录查找
        script_dir = os.path.dirname(os.path.abspath(__file__))
        if script_dir != os.getcwd():
            print(f"当前目录未找到结果文件，尝试在脚本所在目录查找...")
            script_dir_files = glob.glob(os.path.join(script_dir, 'anomaly_results_*.csv'))
            
            if script_dir_files:
                print(f"在脚本所在目录找到 {len(script_dir_files)} 个结果文件")
                return script_dir_files
        
        # 自动在上级目录查找
        parent_dir = os.path.abspath(os.path.join(os.getcwd(), os.pardir))
        parent_result_files = glob.glob(os.path.join(parent_dir, 'anomaly_results_*.csv'))
        
        if parent_result_files:
            print(f"在上级目录找到 {len(parent_result_files)} 个结果文件")
            return parent_result_files
        
        # 自动递归查找子目录
        print("尝试在子目录中查找...")
        for root, dirs, files in os.walk(os.getcwd()):
            for file in files:
                if file.startswith('anomaly_results_') and file.endswith('.csv'):
                    result_files.append(os.path.join(root, file))
        
        if result_files:
            print(f"在子目录中找到 {len(result_files)} 个结果文件")
            return result_files
        
        print("未找到任何结果文件！请修改脚本中的RESULT_DIR变量，指定包含结果文件的目录")
                
    return result_files

def save_to_subfolder(result_files):
    """将分析结果保存到子文件夹中"""
    # 获取结果文件所在目录
    result_dir = os.path.dirname(result_files[0]) if result_files else os.getcwd()
    
    # 创建结果子文件夹
    results_folder = os.path.join(result_dir, "分析结果_" + datetime.now().strftime("%Y%m%d_%H%M%S"))
    if not os.path.exists(results_folder):
        os.makedirs(results_folder)
    
    # 收集所有井的高风险数据，使用字典按井名和卡钻时间组织
    well_risks = {}
    file_well_map = {}  # 记录文件名和提取的井名的映射关系
    
    # 为每个文件生成分析结果并保存
    for csv_file in result_files:
        # 生成输出文件名
        file_base = os.path.splitext(os.path.basename(csv_file))[0]
        txt_file = os.path.join(results_folder, f"{file_base}_分析结果.txt")
        
        # 分析文件并保存结果
        try:
            result_text, high_risk_periods = analyze_result_file(csv_file)
            
            # 保存到txt文件
            with open(txt_file, 'w', encoding='utf-8') as f:
                f.write(result_text)
            
            # 提取井名和卡钻时间
            orig_filename = os.path.basename(csv_file)
            well_name, stuck_time = extract_well_info(orig_filename)
            file_well_map[orig_filename] = (well_name, stuck_time)  # 记录映射关系
            
            # 解析卡钻时间字符串为datetime对象
            stuck_datetime = None
            if stuck_time not in ["不卡钻", "未知"]:
                try:
                    # 尝试解析YYYY/MM/DD HH:MM格式
                    stuck_datetime = pd.to_datetime(stuck_time)
                    print(f"已解析卡钻时间: {stuck_time} -> {stuck_datetime}")
                except Exception as e:
                    print(f"无法解析卡钻时间: {stuck_time}，错误: {e}，将保留所有高风险预警")
            
            # 汇总该井的高风险时段
            key = (well_name, stuck_time)
            if key not in well_risks:
                well_risks[key] = []
                
            if high_risk_periods:
                filtered_periods = []
                
                for risk in high_risk_periods:
                    # 检查高风险预警是否发生在卡钻时间之前
                    if stuck_datetime is not None:
                        # 只保留发生在卡钻时间之前的预警
                        if risk['start_time'] > stuck_datetime:
                            print(f"跳过时间段: {risk['start_time']} (晚于卡钻时间 {stuck_datetime})")
                            continue  # 跳过卡钻时间之后的预警
                        else:
                            print(f"保留时间段: {risk['start_time']} (早于卡钻时间 {stuck_datetime})")
                            filtered_periods.append(risk)
                    else:
                        filtered_periods.append(risk)
                
                # 如果有经过过滤的高风险期，添加到结果中        
                for risk in filtered_periods:
                    risk_desc = f"{risk['start_time'].strftime('%m%d %H:%M')}到{risk['end_time'].strftime('%m%d %H:%M')}, 持续 {risk['risk_duration_min']:.0f} 分钟"
                    well_risks[key].append(risk_desc)
                
                print(f"共有 {len(high_risk_periods)} 个高风险时段，过滤后保留 {len(filtered_periods)} 个")
            
            # 如果没有高风险时段，记录为"未检测到高风险预警"
            if key in well_risks and len(well_risks[key]) == 0:
                well_risks[key].append("无大于五分钟预警")
            
            print(f"已生成分析报告: {txt_file}")
            
        except Exception as e:
            print(f"处理文件 {csv_file} 时出错: {str(e)}")
            print(f"错误类型: {type(e).__name__}")
            import traceback
            traceback.print_exc()
    
    # 保存井名提取映射关系到文件
    well_map_file = os.path.join(results_folder, "井名提取映射.csv")
    with open(well_map_file, 'w', encoding='utf-8-sig') as f:
        f.write("文件名,提取井名,卡钻时间\n")
        for filename, (wellname, stucktime) in file_well_map.items():
            f.write(f"{filename},{wellname},{stucktime}\n")
    
    # 保存所有井的高风险数据到CSV（合并同一井的多个高风险时段到一个单元格）
    if well_risks:
        # 将字典转换为DataFrame
        risk_data = []
        for (well, stuck_time), risks in well_risks.items():
            risk_text = "\n".join(risks)
            risk_data.append({'井名': well, '卡钻时间': stuck_time, '高风险时段': risk_text})
            
        risk_df = pd.DataFrame(risk_data)
        risk_csv = os.path.join(results_folder, "井高风险时段汇总.csv")
        risk_df.to_csv(risk_csv, index=False, encoding='utf-8-sig')
        print(f"已保存高风险时段汇总: {risk_csv}")
    
    print(f"\n分析完成！所有报告已保存到 '{results_folder}' 文件夹")
    print(f"共发现 {len(well_risks)} 个井-卡钻组合，原始文件 {len(result_files)} 个")

def save_to_single_file(result_files):
    """将所有分析结果合并到一个文件中"""
    # 获取结果文件所在目录
    result_dir = os.path.dirname(result_files[0]) if result_files else os.getcwd()
    
    # 创建输出文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = os.path.join(result_dir, f"卡钻分析结果汇总_{timestamp}.txt")
    all_results = f"卡钻检测分析报告汇总\n生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
    
    # 收集所有井的高风险数据，使用字典按井名和卡钻时间组织
    well_risks = {}
    file_well_map = {}  # 记录文件名和提取的井名的映射关系
    
    # 分析所有文件
    for csv_file in result_files:
        try:
            # 分析文件
            result_text, high_risk_periods = analyze_result_file(csv_file)
            all_results += result_text
            
            # 提取井名和卡钻时间
            orig_filename = os.path.basename(csv_file)
            well_name, stuck_time = extract_well_info(orig_filename)
            file_well_map[orig_filename] = (well_name, stuck_time)  # 记录映射关系
            
            # 解析卡钻时间字符串为datetime对象
            stuck_datetime = None
            if stuck_time not in ["不卡钻", "未知"]:
                try:
                    # 尝试解析YYYY/MM/DD HH:MM格式
                    stuck_datetime = pd.to_datetime(stuck_time)
                    print(f"已解析卡钻时间: {stuck_time} -> {stuck_datetime}")
                except Exception as e:
                    print(f"无法解析卡钻时间: {stuck_time}，错误: {e}，将保留所有高风险预警")
            
            # 汇总该井的高风险时段
            key = (well_name, stuck_time)
            if key not in well_risks:
                well_risks[key] = []
                
            if high_risk_periods:
                filtered_periods = []
                
                for risk in high_risk_periods:
                    # 检查高风险预警是否发生在卡钻时间之前
                    if stuck_datetime is not None:
                        # 只保留发生在卡钻时间之前的预警
                        if risk['start_time'] > stuck_datetime:
                            print(f"跳过时间段: {risk['start_time']} (晚于卡钻时间 {stuck_datetime})")
                            continue  # 跳过卡钻时间之后的预警
                        else:
                            print(f"保留时间段: {risk['start_time']} (早于卡钻时间 {stuck_datetime})")
                            filtered_periods.append(risk)
                    else:
                        filtered_periods.append(risk)
                
                # 如果有经过过滤的高风险期，添加到结果中        
                for risk in filtered_periods:
                    risk_desc = f"{risk['start_time'].strftime('%m%d %H:%M')}到{risk['end_time'].strftime('%m%d %H:%M')}, 持续 {risk['risk_duration_min']:.0f} 分钟"
                    well_risks[key].append(risk_desc)
                
                print(f"共有 {len(high_risk_periods)} 个高风险时段，过滤后保留 {len(filtered_periods)} 个")
            
            # 如果没有高风险时段，记录为"未检测到高风险预警"
            if key in well_risks and len(well_risks[key]) == 0:
                well_risks[key].append("无大于五分钟预警")
                
            print(f"已分析文件: {os.path.basename(csv_file)}")
        except Exception as e:
            print(f"处理文件 {csv_file} 时出错: {str(e)}")
            print(f"错误类型: {type(e).__name__}")
            import traceback
            traceback.print_exc()
            all_results += f"分析文件: {os.path.basename(csv_file)}\n错误: {e}\n\n"
    
    # 保存到单一文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(all_results)
    
    # 保存井名提取映射关系到文件
    well_map_file = os.path.join(result_dir, f"井名提取映射_{timestamp}.csv")
    with open(well_map_file, 'w', encoding='utf-8-sig') as f:
        f.write("文件名,提取井名,卡钻时间\n")
        for filename, (wellname, stucktime) in file_well_map.items():
            f.write(f"{filename},{wellname},{stucktime}\n")
    
    # 保存所有井的高风险数据到CSV（合并同一井的多个高风险时段到一个单元格）
    if well_risks:
        # 将字典转换为DataFrame
        risk_data = []
        for (well, stuck_time), risks in well_risks.items():
            risk_text = "\n".join(risks)
            risk_data.append({'井名': well, '卡钻时间': stuck_time, '高风险时段': risk_text})
            
        risk_df = pd.DataFrame(risk_data)
        risk_csv = os.path.join(result_dir, f"井高风险时段汇总_{timestamp}.csv")
        risk_df.to_csv(risk_csv, index=False, encoding='utf-8-sig')
        print(f"已保存高风险时段汇总: {risk_csv}")
    
    print(f"\n分析完成！所有报告已合并保存到 '{output_file}'")
    print(f"共发现 {len(well_risks)} 个井-卡钻组合，原始文件 {len(result_files)} 个")

def parse_args():
    parser = argparse.ArgumentParser(description='异常检测结果分析工具')
    parser.add_argument('--dir', '-d', type=str, help='指定包含结果文件的目录路径')
    parser.add_argument('--mode', '-m', type=int, choices=[1, 2], help='保存模式: 1-子文件夹, 2-单文件')
    return parser.parse_args()

def main():
    print("=" * 60)
    print("卡钻异常检测结果分析工具")
    print("=" * 60)
    
    # 显示当前设置
    if RESULT_DIR:
        print(f"结果文件目录: {RESULT_DIR}")
    else:
        print("结果文件目录: 未指定（自动查找）")
    
    print(f"保存模式: {'单独文件夹' if SAVE_MODE == 1 else '合并文件'}")
    print("=" * 60)
    
    print("\n正在查找异常检测结果文件...")
    # 自动查找CSV文件
    result_files = find_csv_files()
    
    if not result_files:
        print("\n未找到任何异常检测结果文件！请检查以下几点：")
        print("1. 确保结果文件名以'anomaly_results_'开头，并以'.csv'结尾")
        print("2. 在脚本顶部设置RESULT_DIR变量，指定包含结果文件的目录")
        input("\n按任意键退出...")
        return
    
    print(f"\n找到 {len(result_files)} 个结果文件:")
    for i, file in enumerate(result_files):
        print(f"  {i+1}. {file}")
    
    # 测试井名和卡钻时间提取
    print("\n测试井名和卡钻时间提取:")
    test_names = {os.path.basename(f): extract_well_info(os.path.basename(f)) for f in result_files[:min(10, len(result_files))]}
    for original, (well_name, stuck_time) in test_names.items():
        print(f"  原始文件: {original} -> 提取井名: {well_name}, 卡钻时间: {stuck_time}")
    
    # 询问是否继续
    print("\n如果井名或卡钻时间提取不正确，请修改脚本中的extract_well_info函数后重新运行")
    cont = input("是否继续处理? (y/n): ").strip().lower()
    if cont != 'y':
        print("已取消处理")
        return
    
    # 根据保存模式选择保存方法
    if SAVE_MODE == 1:
        print("\n使用模式1: 将结果保存到子文件夹中")
        save_to_subfolder(result_files)
    else:
        print("\n使用模式2: 将所有结果合并到一个文件中")
        save_to_single_file(result_files)
    
    print("\n分析完成！按任意键退出...")
    input()

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n程序运行出错: {str(e)}")
        print(f"错误类型: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        print("\n按任意键退出...")
        input()

