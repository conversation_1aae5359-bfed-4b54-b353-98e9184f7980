import numpy as np
from scipy import stats
from scipy.signal import savgol_filter, find_peaks
import torch
import os
import time


class StuckPipeDetector:
    """卡钻检测器类，专门处理扭矩和大钩负荷数据"""

    def __init__(self, params=None, use_gpu=True):
        """
        初始化卡钻检测器

        参数:
            params: 检测参数字典
            use_gpu: 是否使用GPU加速
        """
        # 设置默认参数
        self.params = {
            'window_size': 100,  # 滑动窗口大小
            'torque_threshold': 1.0,  # 扭矩异常阈值(标准差倍数)
            'hook_load_threshold': 1.5,  # 大钩负荷异常阈值(标准差倍数)
            'cross_corr_window': 30,  # 互相关窗口大小
            'cross_corr_threshold': -0.4,  # 互相关阈值
            'rate_change_threshold': 0.2,  # 变化率阈值
            'score_threshold': 0.5,  # 综合分数阈值
            'min_anomaly_duration': 3,  # 最小异常持续点数
            'smooth_window': 7  # 信号平滑窗口大小
        }

        # 更新用户参数
        if params is not None:
            self.params.update(params)
            
        # GPU设置
        self.use_gpu = use_gpu and torch.cuda.is_available()
        if self.use_gpu:
            self.device = torch.device('cuda')
            # 检查PyTorch版本
            torch_version = torch.__version__
            cuda_version = torch.version.cuda if hasattr(torch.version, 'cuda') else "未知"
            # print(f"[GPU信息] 使用GPU加速卡钻检测: {torch.cuda.get_device_name(0)}")
            # print(f"[GPU信息] PyTorch版本: {torch_version}, CUDA版本: {cuda_version}")
            
            # 设置更高的CUDA性能模式
            try:
                # 尝试设置为最高性能模式
                if hasattr(torch.backends.cudnn, 'benchmark'):
                    torch.backends.cudnn.benchmark = True
                    # print("[GPU优化] CUDNN性能优化已启用")
                    
                # 提升GPU优先级，使其占用更多GPU资源
                if hasattr(torch.cuda, 'set_device_flags') and hasattr(torch.cuda, 'device_flags'):
                    try:
                        # 实验性功能，可能不是所有PyTorch版本都支持
                        torch.cuda.set_device_flags(torch.cuda.device_flags.gpu_yield_always)
                        # print("[GPU优化] 设置GPU为高优先级模式")
                    except:
                        pass
            except Exception as e:
                print(f"[GPU警告] 无法设置高性能模式: {e}")
        else:
            self.device = torch.device('cpu')
            if use_gpu:
                print("[警告] 请求了GPU加速，但系统未检测到可用的CUDA设备，将使用CPU计算")
            
        # 创建缓存目录
        self.cache_dir = './.cache'
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)

    def _get_cache_path(self, data_hash):
        """获取缓存文件路径"""
        return os.path.join(self.cache_dir, f'stuck_pipe_results_{data_hash}.npz')

    def detect(self, data, torque_col_idx=7, hook_load_col_idx=5):
        """
        执行卡钻检测

        参数:
            data: numpy数组，包含多个特征的时间序列数据
            torque_col_idx: 扭矩列索引
            hook_load_col_idx: 大钩负荷列索引

        返回:
            anomalies: 异常点的布尔数组
            scores: 异常分数
        """
        # 记录开始时间
        start_time = time.time()
        
        # 记录原始形状，用于缓存
        original_shape = data.shape
        
        # 尝试使用缓存
        if os.path.exists(self.cache_dir):
            simple_hash = f"{original_shape}_{torque_col_idx}_{hook_load_col_idx}"
            data_hash = str(hash(simple_hash))
            cache_path = self._get_cache_path(data_hash)
            
            if os.path.exists(cache_path):
                try:
                    # print(f"[性能优化] 从缓存加载卡钻检测结果")
                    cached_results = np.load(cache_path)
                    # print(f"[性能优化] 缓存加载完成，用时: {time.time() - start_time:.3f}秒")
                    return cached_results['anomalies'], cached_results['scores']
                except Exception as e:
                    print(f"[性能优化] 缓存加载失败: {e}")
        
        # 提取扭矩和大钩负荷数据
        if len(data.shape) > 2:  # 处理三维数据 [batch, seq_len, feature]
            # print(f"[性能优化] 处理三维数据: 原始形状={original_shape}")
            # 确保数据形状正确
            flattened_length = data.shape[0] * data.shape[1]
            torque = data[:, :, torque_col_idx].reshape(flattened_length)
            hook_load = data[:, :, hook_load_col_idx].reshape(flattened_length)
            # print(f"[性能优化] 扁平化后数据长度: {flattened_length}")
        else:  # 处理二维数据 [seq_len, feature]
            torque = data[:, torque_col_idx]
            hook_load = data[:, hook_load_col_idx]

        # 数据有效性检查
        if len(torque) < self.params['window_size'] or len(hook_load) < self.params['window_size']:
            print(f"警告: 数据长度不足 ({len(torque)}), 需要至少 {self.params['window_size']} 个样本")
            return np.zeros(len(torque), dtype=bool), np.zeros(len(torque))

        # 数据预处理 - 填充缺失值和平滑处理
        # print("[性能优化] 开始数据预处理...")
        torque = self._replace_nan_and_inf(torque)
        hook_load = self._replace_nan_and_inf(hook_load)
        
        preprocess_time = time.time()
        # print(f"[性能统计] 预处理耗时: {preprocess_time - start_time:.3f}秒")

        try:
            # 尝试平滑信号
            smooth_window = min(self.params['smooth_window'], len(torque) - 1)
            if smooth_window % 2 == 0:
                smooth_window -= 1  # 确保平滑窗口是奇数
            if smooth_window >= 3:
                torque_smooth = savgol_filter(torque, smooth_window, 3)
                hook_load_smooth = savgol_filter(hook_load, smooth_window, 3)
            else:
                # 窗口太小时使用简单平均
                torque_smooth = torque
                hook_load_smooth = hook_load
        except Exception as e:
            print(f"平滑信号时出错: {e}")
            torque_smooth = torque
            hook_load_smooth = hook_load
            
        smooth_time = time.time()
        # print(f"[性能统计] 信号平滑耗时: {smooth_time - preprocess_time:.3f}秒")

        # 检查是否使用GPU加速
        if self.use_gpu:
            try:
                # 打印GPU状态
                if torch.cuda.is_available():
                    device_props = torch.cuda.get_device_properties(0)
                    free_memory = device_props.total_memory - torch.cuda.memory_allocated()
                    # print(f"[GPU信息] 设备: {device_props.name}, 可用显存: {free_memory/1024**3:.1f}GB")
                    # print(f"[GPU信息] CUDA版本: {torch.version.cuda}, PyTorch版本: {torch.__version__}")
                
                # GPU加速路径
                # print("[性能优化] 启用GPU计算...")
                return self._detect_gpu(torque_smooth, hook_load_smooth, data_hash)
            except Exception as e:
                print(f"[性能优化] GPU计算出错: {e}，回退到CPU计算")
                # 记录详细错误信息帮助调试
                import traceback
                print(f"[GPU错误详情] {traceback.format_exc()}")
                torch.cuda.empty_cache()  # 清理GPU内存
        
        # CPU处理路径 (原始代码)
        # print("[性能优化] 使用CPU计算...")
        return self._detect_cpu(torque_smooth, hook_load_smooth, data_hash)
        
    def _detect_gpu(self, torque_smooth, hook_load_smooth, data_hash=None):
        """使用GPU加速的卡钻检测实现 - 高性能版本"""
        # print("[性能优化] 使用GPU加速计算（高性能模式）")
        start_time = time.time()
        n_samples = len(torque_smooth)
        window = self.params['window_size']
        
        try:
            # 1. 优化数据传输 - 一次性将所有数据传到GPU，避免频繁传输
            # 使用混合精度计算，降低内存占用并提高速度
            if torch.cuda.is_available() and hasattr(torch.cuda, 'amp'):
                # 启用自动混合精度
                # print("[性能优化] 启用自动混合精度")
                with torch.cuda.amp.autocast():
                    return self._detect_gpu_core(torque_smooth, hook_load_smooth, n_samples, window, data_hash, start_time)
            else:
                # 不支持混合精度的GPU也能运行
                return self._detect_gpu_core(torque_smooth, hook_load_smooth, n_samples, window, data_hash, start_time)
                
        except Exception as e:
            # GPU计算失败时的详细错误信息
            import traceback
            print(f"[GPU错误详情] {str(e)}")
            print(traceback.format_exc())
            torch.cuda.empty_cache()
            raise e  # 重新抛出异常，让detect方法回退到CPU
            
    def _detect_gpu_core(self, torque_smooth, hook_load_smooth, n_samples, window, data_hash=None, start_time=None):
        """GPU加速的核心实现函数，兼容各种PyTorch版本"""
        # 打印总数据大小，方便调试
        # print(f"[GPU信息] 输入数据大小: {n_samples} 样本")
        
        # 对于超大数据集的特殊优化
        super_large_dataset = n_samples > 1000000
        if super_large_dataset:
            # print(f"[GPU优化] 检测到超大数据集: {n_samples/1000000:.2f}M 样本，启用特殊优化")
            pass
        
        # 针对大数据集做分段计算，避免同时加载所有数据到GPU
        # 将数据分段加载到GPU，每段大小为segment_len
        if super_large_dataset:
            # 超大数据集分段长度
            segment_len = 200000
            # print(f"[GPU优化] 启用分段处理模式，每段处理 {segment_len} 样本")
            return self._detect_gpu_segmented(torque_smooth, hook_load_smooth, n_samples, window, data_hash, start_time, segment_len)
            
        # 将Numpy数组转换为CPU张量，然后再转到GPU
        # 这种方式更兼容所有PyTorch版本
        torque_tensor = torch.tensor(torque_smooth, dtype=torch.float32)
        hook_load_tensor = torch.tensor(hook_load_smooth, dtype=torch.float32)
        
        # 转移到GPU - 使用to()方法，这在所有PyTorch版本上都有效
        torque_tensor = torque_tensor.to(self.device)
        hook_load_tensor = hook_load_tensor.to(self.device)
        
        # 显存自适应设置
        # 针对较大数据集的处理策略
        if n_samples > 500000:
            # print(f"[GPU优化] 大数据集模式: {n_samples/1000000:.2f}M 样本")
            # 对于大数据集，使用更加保守的内存策略
            segment_size = 1000  # 小批量分段处理
            keep_progress_interval = 50000  # 降低进度报告频率
        else:
            segment_size = 2000  # 一般数据集
            keep_progress_interval = 20000

        # 显存监控
        if torch.cuda.is_available():
            free_mem = (torch.cuda.get_device_properties(0).total_memory - torch.cuda.memory_allocated()) / (1024**3)
            # print(f"[GPU内存] 当前可用显存: {free_mem:.2f}GB")
            
            # 动态调整segment_size，根据可用显存大小
            if free_mem < 2.0:  # 显存小于2GB，进一步减小批次大小
                segment_size = max(500, segment_size // 2)
                # print(f"[GPU优化] 显存不足，降低批次大小至 {segment_size}")
            elif free_mem > 8.0:  # 显存充足，可以增大批次大小
                segment_size = min(5000, segment_size * 2)
                # print(f"[GPU优化] 显存充足，增加批次大小至 {segment_size}")
        
        # 使用卷积计算滑动窗口统计量
        # print("[GPU进度] 开始计算Z分数...")
        
        # 使用单次卷积操作处理多个信号
        kernel_size = window
        padding = (kernel_size - 1) // 2
        
        # 优化卷积核使用
        kernel = torch.ones(1, 1, kernel_size, device=self.device) / kernel_size
        
        # 扭矩和大钩负荷的处理可以并行，将两个信号合并为一个批次处理
        combined_signals = torch.stack([torque_tensor, hook_load_tensor])
        signals_3d = combined_signals.unsqueeze(1)
        
        # 计算移动平均和标准差
        means = torch.nn.functional.conv1d(signals_3d, kernel, padding=padding).squeeze(1)
        mean_squares = torch.nn.functional.conv1d(signals_3d ** 2, kernel, padding=padding).squeeze(1)
        variances = torch.clamp(mean_squares - means ** 2, min=1e-8)
        stds = torch.sqrt(variances)
        
        # 提取各个信号的统计量
        torque_mean, hook_load_mean = means[0], means[1]
        torque_std, hook_load_std = stds[0], stds[1]
        
        # 释放不再需要的内存
        del signals_3d, combined_signals, means, mean_squares, variances
        torch.cuda.empty_cache()
        
        # 计算Z分数 - 使用小批次处理避免内存问题
        torque_z_values = torch.zeros_like(torque_tensor)
        hook_load_z_values = torch.zeros_like(hook_load_tensor)
        
        # 批处理Z分数计算
        for start in range(0, n_samples, segment_size):
            end = min(start + segment_size, n_samples)
            
            # 计算当前批次的Z分数
            t_valid = torque_std[start:end] > 0
            h_valid = hook_load_std[start:end] > 0
            
            if torch.any(t_valid):
                valid_indices = start + torch.nonzero(t_valid, as_tuple=True)[0]
                torque_z_values[valid_indices] = ((torque_tensor[valid_indices] - torque_mean[valid_indices]) 
                                            / torque_std[valid_indices])
            
            if torch.any(h_valid):
                valid_indices = start + torch.nonzero(h_valid, as_tuple=True)[0]
                hook_load_z_values[valid_indices] = ((hook_load_tensor[valid_indices] - hook_load_mean[valid_indices])
                                                / hook_load_std[valid_indices])
        
        # 释放不再需要的内存
        del torque_mean, hook_load_mean, torque_std, hook_load_std
        torch.cuda.empty_cache()
        
        # 处理NaN和Inf值
        torque_z_values = torch.nan_to_num(torque_z_values) if hasattr(torch, 'nan_to_num') else self._safe_nan_to_num(torque_z_values)
        hook_load_z_values = torch.nan_to_num(hook_load_z_values) if hasattr(torch, 'nan_to_num') else self._safe_nan_to_num(hook_load_z_values)
        
        # 计算互相关和变化率
        # print("[GPU进度] 开始计算互相关...")
        corr_window = self.params['cross_corr_window']
        half_window = window // 2
        
        # 创建结果张量
        cross_correlation_tensor = torch.zeros(n_samples, device=self.device)
        torque_rate_tensor = torch.zeros(n_samples, device=self.device)
        hook_load_rate_tensor = torch.zeros(n_samples, device=self.device)
        
        # 高效批处理互相关和变化率计算
        # 使用小批次以避免内存问题
        for start in range(corr_window, n_samples, segment_size):
            end = min(start + segment_size, n_samples)
            
            # if start % keep_progress_interval == 0:
            #     print(f"[GPU进度] 处理批次: {start}-{end} / {n_samples} ({start/n_samples*100:.1f}%)")
            #     # 显示内存使用情况
            #     if torch.cuda.is_available():
            #         mem_alloc = torch.cuda.memory_allocated() / 1024**2
            #         print(f"[GPU内存] 当前已分配: {mem_alloc:.1f}MB")
            
            # 只处理有效索引范围
            valid_idx = torch.arange(start, end, device=self.device)
            batch_size = len(valid_idx)
            
            # 计算互相关 - 优化方法
            if corr_window < 1000:  # 对于较小的窗口大小，使用矩阵乘法方式
                # 创建窗口视图
                t_windows = torch.stack([torque_tensor[i-corr_window:i] for i in valid_idx])
                h_windows = torch.stack([hook_load_tensor[i-corr_window:i] for i in valid_idx])
                
                # 计算标准差
                t_stds = torch.std(t_windows, dim=1, keepdim=True)
                h_stds = torch.std(h_windows, dim=1, keepdim=True)
                
                # 过滤有效数据
                valid_mask = (t_stds.squeeze() > 0) & (h_stds.squeeze() > 0)
                valid_idx_filt = valid_idx[valid_mask]
                
                if len(valid_idx_filt) > 0:
                    # 只处理有效数据
                    t_windows_valid = t_windows[valid_mask]
                    h_windows_valid = h_windows[valid_mask]
                    t_stds_valid = t_stds[valid_mask]
                    h_stds_valid = h_stds[valid_mask]
                    
                    # 标准化处理
                    t_norm = (t_windows_valid - torch.mean(t_windows_valid, dim=1, keepdim=True)) / (t_stds_valid + 1e-8)
                    h_norm = (h_windows_valid - torch.mean(h_windows_valid, dim=1, keepdim=True)) / (h_stds_valid + 1e-8)
                    
                    # 计算相关系数
                    corrs = torch.sum(t_norm * h_norm, dim=1) / corr_window
                    cross_correlation_tensor[valid_idx_filt] = corrs
                    
                # 释放内存
                del t_windows, h_windows
                if 'valid_mask' in locals():
                    del valid_mask, t_stds, h_stds
                if 'valid_idx_filt' in locals() and len(valid_idx_filt) > 0:
                    del t_windows_valid, h_windows_valid, t_stds_valid, h_stds_valid, t_norm, h_norm, corrs, valid_idx_filt
            else:
                # 对于大窗口，使用循环方式避免内存问题
                for i, idx in enumerate(valid_idx):
                    if idx < corr_window:
                        continue
                    
                    # 获取窗口数据
                    t_window = torque_tensor[idx-corr_window:idx]
                    h_window = hook_load_tensor[idx-corr_window:idx]
                    
                    # 计算标准差
                    t_std = torch.std(t_window)
                    h_std = torch.std(h_window)
                    
                    # 只处理有效数据
                    if t_std > 0 and h_std > 0:
                        # 标准化
                        t_mean = torch.mean(t_window)
                        h_mean = torch.mean(h_window)
                        
                        t_norm = (t_window - t_mean) / t_std
                        h_norm = (h_window - h_mean) / h_std
                        
                        # 计算相关系数
                        corr = torch.sum(t_norm * h_norm) / corr_window
                        cross_correlation_tensor[idx] = corr
            
            # 计算变化率 - 直接方式处理
            for i, idx in enumerate(valid_idx):
                if idx < window:
                    continue
                
                # 直接计算平均值
                t_prev = torch.mean(torque_tensor[idx-window:idx-half_window])
                t_curr = torch.mean(torque_tensor[idx-half_window:idx])
                
                h_prev = torch.mean(hook_load_tensor[idx-window:idx-half_window])
                h_curr = torch.mean(hook_load_tensor[idx-half_window:idx])
                
                # 计算变化率
                if torch.abs(t_prev) > 1e-6:
                    torque_rate_tensor[idx] = (t_curr - t_prev) / torch.abs(t_prev)
                
                if torch.abs(h_prev) > 1e-6:
                    hook_load_rate_tensor[idx] = (h_curr - h_prev) / torch.abs(h_prev)
            
            # 每个批次处理完成后释放缓存
            torch.cuda.empty_cache()
            
        # 释放不再需要的内存
        torch.cuda.empty_cache()
        
        # 计算各种异常分数
        # print("[GPU进度] 计算异常分数...")
        
        # 在GPU上批量计算分数
        # 扭矩异常分数
        torque_score_tensor = torch.zeros_like(torque_tensor)
        hook_load_score_tensor = torch.zeros_like(hook_load_tensor)
        correlation_score_tensor = torch.zeros_like(torque_tensor)
        rate_score_tensor = torch.zeros_like(torque_tensor)
        
        # 分段处理以避免内存问题
        for start in range(0, n_samples, segment_size):
            end = min(start + segment_size, n_samples)
            
            # 计算各种分数
            torque_score_tensor[start:end] = torch.clamp(
                torch.abs(torque_z_values[start:end]) / self.params['torque_threshold'], 
                0, 1
            )
            
            hook_load_score_tensor[start:end] = torch.clamp(
                torch.abs(hook_load_z_values[start:end]) / self.params['hook_load_threshold'], 
                0, 1
            )
            
            # 互相关异常分数
            neg_corr = cross_correlation_tensor[start:end] < self.params['cross_corr_threshold']
            if torch.any(neg_corr) and self.params['cross_corr_threshold'] < 1:
                valid_indices = start + torch.nonzero(neg_corr, as_tuple=True)[0]
                correlation_score_tensor[valid_indices] = (
                    self.params['cross_corr_threshold'] - cross_correlation_tensor[valid_indices]
                ) / (1 - self.params['cross_corr_threshold'])
            
            # 变化率异常分数
            rate_threshold = self.params['rate_change_threshold']
            rate_score_tensor[start:end] = torch.clamp(
                torch.abs(torque_rate_tensor[start:end]) / rate_threshold + 
                torch.abs(hook_load_rate_tensor[start:end]) / rate_threshold,
                0, 1
            )
        
        # 释放不再需要的内存
        del torque_z_values, hook_load_z_values, cross_correlation_tensor
        del torque_rate_tensor, hook_load_rate_tensor
        torch.cuda.empty_cache()
        
        # 计算综合分数
        # print("[GPU进度] 计算综合分数和标记异常...")
        weights = {
            'torque_score': 0.35,
            'hook_load_score': 0.35,
            'correlation_score': 0.2,
            'rate_score': 0.1
        }
        
        # 分段处理计算综合分数
        combined_score_tensor = torch.zeros_like(torque_tensor)
        for start in range(0, n_samples, segment_size):
            end = min(start + segment_size, n_samples)
            
            combined_score_tensor[start:end] = (
                weights['torque_score'] * torque_score_tensor[start:end] +
                weights['hook_load_score'] * hook_load_score_tensor[start:end] +
                weights['correlation_score'] * correlation_score_tensor[start:end] +
                weights['rate_score'] * rate_score_tensor[start:end]
            )
        
        # 标记异常点
        anomalies_tensor = combined_score_tensor > self.params['score_threshold']
        
        # 释放不再需要的内存
        del torque_score_tensor, hook_load_score_tensor, correlation_score_tensor, rate_score_tensor
        torch.cuda.empty_cache()
        
        # 将结果转移到CPU
        # print("[GPU进度] 处理结果并转移到CPU...")
        anomalies_np = anomalies_tensor.cpu().numpy()
        combined_score = combined_score_tensor.cpu().numpy()
        
        # 释放最后的GPU内存
        del anomalies_tensor, combined_score_tensor
        torch.cuda.empty_cache()
        
        # 在CPU上处理连续异常
        # print("[CPU进度] 聚合连续异常...")
        anomalies_processed = np.zeros_like(anomalies_np)
        in_anomaly = False
        anomaly_start = 0
        
        for i in range(len(anomalies_np)):
            if anomalies_np[i] and not in_anomaly:
                in_anomaly = True
                anomaly_start = i
            elif not anomalies_np[i] and in_anomaly:
                in_anomaly = False
                # 只保留持续时间达到阈值的异常
                if i - anomaly_start >= self.params['min_anomaly_duration']:
                    anomalies_processed[anomaly_start:i] = True
        
        # 处理最后一个异常段
        if in_anomaly and len(anomalies_np) - anomaly_start >= self.params['min_anomaly_duration']:
            anomalies_processed[anomaly_start:] = True
        
        # 保存结果到缓存
        if data_hash is not None:
            try:
                cache_path = self._get_cache_path(data_hash)
                np.savez(cache_path, anomalies=anomalies_processed, scores=combined_score)
                # print(f"[性能优化] 卡钻检测结果已缓存到: {cache_path}")
            except Exception as e:
                print(f"[性能优化] 缓存保存失败: {e}")
        
        # 清理内存
        self._clean_gpu_memory()
        
        # 统计时间和异常比例
        total_time = time.time() - start_time
        print(f"[性能统计] GPU加速检测总耗时: {total_time:.3f}秒，异常比例: {np.mean(anomalies_processed)*100:.2f}%")
        # print(f"[性能优化] GPU计算完成.")
        
        return anomalies_processed, combined_score
        
    def _detect_gpu_segmented(self, torque_smooth, hook_load_smooth, n_samples, window, data_hash=None, start_time=None, segment_len=200000):
        """超大数据集分段处理方法 - 避免一次性加载所有数据到GPU"""
        # print(f"[GPU优化] 使用分段处理，数据总长度: {n_samples}，分段长度: {segment_len}")
        
        # 确保分段大小合理
        segment_len = min(segment_len, n_samples)
        
        # 创建结果数组
        anomalies_processed = np.zeros(n_samples, dtype=bool)
        combined_score = np.zeros(n_samples)
        
        # 分段处理
        for start in range(0, n_samples, segment_len):
            end = min(start + segment_len, n_samples)
            overlap = 100  # 边界重叠，避免边界问题
            
            # 创建带重叠的段
            seg_start = max(0, start - overlap)
            seg_end = min(n_samples, end + overlap)
            
            # print(f"[GPU分段] 处理段 {start}-{end} (含重叠: {seg_start}-{seg_end})")
            
            # 提取当前段数据
            torque_seg = torque_smooth[seg_start:seg_end]
            hook_load_seg = hook_load_smooth[seg_start:seg_end]
            
            # 处理当前段
            seg_anomalies, seg_scores = self._process_data_segment(
                torque_seg, hook_load_seg, len(torque_seg), window, start_time
            )
            
            # 去除重叠部分，填充结果
            valid_start = 0 if seg_start == 0 else overlap
            valid_end = len(seg_anomalies) if seg_end == n_samples else len(seg_anomalies) - overlap
            
            # 填充结果
            anomalies_processed[start:end] = seg_anomalies[valid_start:valid_end]
            combined_score[start:end] = seg_scores[valid_start:valid_end]
            
            # 清理内存
            self._clean_gpu_memory()
            # print(f"[GPU分段] 完成段 {start}-{end}, 进度: {end/n_samples*100:.1f}%")
        
        # 处理连续异常 - CPU上进行
        anomalies_processed = self._process_continuous_anomalies(anomalies_processed)
        
        # 保存结果到缓存
        if data_hash is not None:
            try:
                cache_path = self._get_cache_path(data_hash)
                np.savez(cache_path, anomalies=anomalies_processed, scores=combined_score)
                # print(f"[性能优化] 卡钻检测结果已缓存到: {cache_path}")
            except Exception as e:
                print(f"[性能优化] 缓存保存失败: {e}")
                
        # 统计时间和异常比例
        total_time = time.time() - start_time
        print(f"[性能统计] 分段GPU加速总耗时: {total_time:.3f}秒，异常比例: {np.mean(anomalies_processed)*100:.2f}%")
        
        return anomalies_processed, combined_score
    
    def _process_data_segment(self, torque_seg, hook_load_seg, n_samples, window, start_time):
        """处理单个数据段"""
        # 实现与_detect_gpu_core类似，但对单个段进行处理
        # 将数据放入GPU
        torque_tensor = torch.tensor(torque_seg, dtype=torch.float32).to(self.device)
        hook_load_tensor = torch.tensor(hook_load_seg, dtype=torch.float32).to(self.device)
        
        # 使用较小的段大小以降低内存使用
        segment_size = 1000
        
        # 以下过程与_detect_gpu_core类似，但简化了一些步骤以提高分段处理效率
        # 计算Z分数
        kernel_size = window
        padding = (kernel_size - 1) // 2
        kernel = torch.ones(1, 1, kernel_size, device=self.device) / kernel_size
        
        # 计算均值和标准差
        combined_signals = torch.stack([torque_tensor, hook_load_tensor])
        signals_3d = combined_signals.unsqueeze(1)
        
        means = torch.nn.functional.conv1d(signals_3d, kernel, padding=padding).squeeze(1)
        mean_squares = torch.nn.functional.conv1d(signals_3d ** 2, kernel, padding=padding).squeeze(1)
        variances = torch.clamp(mean_squares - means ** 2, min=1e-8)
        stds = torch.sqrt(variances)
        
        torque_mean, hook_load_mean = means[0], means[1]
        torque_std, hook_load_std = stds[0], stds[1]
        
        # 释放内存
        del signals_3d, combined_signals, means, mean_squares, variances
        torch.cuda.empty_cache()
        
        # 计算Z分数
        torque_z_values = torch.zeros_like(torque_tensor)
        hook_load_z_values = torch.zeros_like(hook_load_tensor)
        
        # 标准差大于零的位置
        torque_valid = torque_std > 0
        hook_load_valid = hook_load_std > 0
        
        # 计算Z分数
        if torch.any(torque_valid):
            valid_indices = torch.nonzero(torque_valid, as_tuple=True)[0]
            torque_z_values[valid_indices] = ((torque_tensor[valid_indices] - torque_mean[valid_indices]) 
                                        / torque_std[valid_indices])
        
        if torch.any(hook_load_valid):
            valid_indices = torch.nonzero(hook_load_valid, as_tuple=True)[0]
            hook_load_z_values[valid_indices] = ((hook_load_tensor[valid_indices] - hook_load_mean[valid_indices])
                                            / hook_load_std[valid_indices])
        
        # 处理NaN和Inf
        torque_z_values = torch.nan_to_num(torque_z_values) if hasattr(torch, 'nan_to_num') else self._safe_nan_to_num(torque_z_values)
        hook_load_z_values = torch.nan_to_num(hook_load_z_values) if hasattr(torch, 'nan_to_num') else self._safe_nan_to_num(hook_load_z_values)
        
        # 释放内存
        del torque_mean, hook_load_mean, torque_std, hook_load_std
        torch.cuda.empty_cache()
        
        # 简化版异常分数计算 - 直接使用Z分数
        # 扭矩异常分数
        torque_score = torch.abs(torque_z_values) / self.params['torque_threshold']
        torque_score = torch.clamp(torque_score, 0, 1)
        
        # 大钩负荷异常分数
        hook_load_score = torch.abs(hook_load_z_values) / self.params['hook_load_threshold']
        hook_load_score = torch.clamp(hook_load_score, 0, 1)
        
        # 释放内存
        del torque_z_values, hook_load_z_values
        torch.cuda.empty_cache()
        
        # 简化的综合分数
        combined_score = 0.5 * (torque_score + hook_load_score)
        
        # 标记异常点
        anomalies = combined_score > self.params['score_threshold']
        
        # 转到CPU
        anomalies_np = anomalies.cpu().numpy()
        combined_score_np = combined_score.cpu().numpy()
        
        # 释放GPU内存
        del torque_tensor, hook_load_tensor, torque_score, hook_load_score, combined_score, anomalies
        torch.cuda.empty_cache()
        
        return anomalies_np, combined_score_np
        
    def _process_continuous_anomalies(self, anomalies):
        """处理连续异常点"""
        anomalies_processed = np.zeros_like(anomalies)
        in_anomaly = False
        anomaly_start = 0
        
        for i in range(len(anomalies)):
            if anomalies[i] and not in_anomaly:
                in_anomaly = True
                anomaly_start = i
            elif not anomalies[i] and in_anomaly:
                in_anomaly = False
                # 只保留持续时间达到阈值的异常
                if i - anomaly_start >= self.params['min_anomaly_duration']:
                    anomalies_processed[anomaly_start:i] = True
        
        # 处理最后一个异常段
        if in_anomaly and len(anomalies) - anomaly_start >= self.params['min_anomaly_duration']:
            anomalies_processed[anomaly_start:] = True
            
        return anomalies_processed

    def _clean_gpu_memory(self):
        """清理GPU内存"""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            # if hasattr(torch.cuda, 'memory_summary'):
            #     print(f"[GPU内存] 清理前内存状态:\n{torch.cuda.memory_summary(abbreviated=True)}")
            torch.cuda.empty_cache()
            # print(f"[GPU内存] 已释放GPU内存")
    
    def _safe_nan_to_num(self, tensor):
        """安全处理NaN和Inf值（适用于不支持nan_to_num的PyTorch版本）"""
        # 替换NaN
        tensor_no_nan = torch.where(torch.isnan(tensor), torch.zeros_like(tensor), tensor)
        # 替换+Inf
        tensor_no_inf = torch.where(tensor_no_nan == float('inf'), torch.ones_like(tensor) * 1e6, tensor_no_nan)
        # 替换-Inf
        return torch.where(tensor_no_inf == float('-inf'), torch.ones_like(tensor) * -1e6, tensor_no_inf)

    def _detect_cpu(self, torque_smooth, hook_load_smooth, data_hash=None):
        """使用CPU执行卡钻检测 (保持原有实现)"""
        # print("[性能优化] 使用CPU执行计算")
        
        # 1. 计算时域特征
        window = self.params['window_size']
        
        # 初始化特征数组
        n_samples = len(torque_smooth)
        torque_diff = np.zeros(n_samples)
        torque_diff2 = np.zeros(n_samples)
        hook_load_diff = np.zeros(n_samples)
        hook_load_diff2 = np.zeros(n_samples)
        torque_mean = np.zeros(n_samples)
        torque_std = np.zeros(n_samples)
        hook_load_mean = np.zeros(n_samples)
        hook_load_std = np.zeros(n_samples)
        torque_z = np.zeros(n_samples)
        hook_load_z = np.zeros(n_samples)
        
        # 计算差分
        torque_diff[1:] = np.diff(torque_smooth)
        torque_diff2[2:] = np.diff(torque_diff[1:])
        hook_load_diff[1:] = np.diff(hook_load_smooth)
        hook_load_diff2[2:] = np.diff(hook_load_diff[1:])
        
        # 计算滚动统计量
        for i in range(window, n_samples):
            torque_window = torque_smooth[i - window:i]
            hook_window = hook_load_smooth[i - window:i]
            
            torque_mean[i] = np.mean(torque_window)
            torque_std[i] = np.std(torque_window)
            hook_load_mean[i] = np.mean(hook_window)
            hook_load_std[i] = np.std(hook_window)
            
            if torque_std[i] > 0:
                torque_z[i] = (torque_smooth[i] - torque_mean[i]) / torque_std[i]
            if hook_load_std[i] > 0:
                hook_load_z[i] = (hook_load_smooth[i] - hook_load_mean[i]) / hook_load_std[i]
        
        # 2. 计算互相关特征
        cross_correlation = np.zeros(n_samples)
        corr_window = self.params['cross_corr_window']
        
        for i in range(corr_window, n_samples):
            t_window = torque_smooth[i - corr_window:i]
            h_window = hook_load_smooth[i - corr_window:i]
            
            if np.std(t_window) > 0 and np.std(h_window) > 0:
                corr = np.corrcoef(t_window, h_window)[0, 1]
                cross_correlation[i] = corr
        
        # 3. 计算变化率特征 (替代小波特征)
        # 计算相对变化率
        torque_rate = np.zeros(n_samples)
        hook_load_rate = np.zeros(n_samples)
        
        for i in range(window, n_samples):
            t_prev = np.mean(torque_smooth[i - window:i - window // 2])
            t_curr = np.mean(torque_smooth[i - window // 2:i])
            
            h_prev = np.mean(hook_load_smooth[i - window:i - window // 2])
            h_curr = np.mean(hook_load_smooth[i - window // 2:i])
            
            # 计算相对变化率
            if abs(t_prev) > 1e-6:
                torque_rate[i] = (t_curr - t_prev) / abs(t_prev)
            if abs(h_prev) > 1e-6:
                hook_load_rate[i] = (h_curr - h_prev) / abs(h_prev)
        
        # 4. 计算异常分数
        # 扭矩异常分数
        torque_score = np.abs(torque_z) / self.params['torque_threshold']
        torque_score = np.clip(torque_score, 0, 1)
        
        # 大钩负荷异常分数
        hook_load_score = np.abs(hook_load_z) / self.params['hook_load_threshold']
        hook_load_score = np.clip(hook_load_score, 0, 1)
        
        # 互相关异常分数
        correlation_score = np.zeros(n_samples)
        negative_corr = cross_correlation < self.params['cross_corr_threshold']
        if self.params['cross_corr_threshold'] < 1:  # 避免除以零
            correlation_score[negative_corr] = (self.params['cross_corr_threshold'] - cross_correlation[
                negative_corr]) / (1 - self.params['cross_corr_threshold'])
        correlation_score = np.clip(correlation_score, 0, 1)
        
        # 变化率异常分数
        rate_threshold = self.params['rate_change_threshold']
        rate_score = np.clip(
            np.abs(torque_rate) / rate_threshold + np.abs(hook_load_rate) / rate_threshold,
            0,
            1
        )
        
        # 5. 综合异常分数
        weights = {
            'torque_score': 0.35,
            'hook_load_score': 0.35,
            'correlation_score': 0.2,
            'rate_score': 0.1
        }
        
        combined_score = (
                weights['torque_score'] * torque_score +
                weights['hook_load_score'] * hook_load_score +
                weights['correlation_score'] * correlation_score +
                weights['rate_score'] * rate_score
        )
        
        # 6. 标记异常点并进行后处理
        anomalies = combined_score > self.params['score_threshold']
        
        # 聚类连续异常点，去除孤立点
        anomalies_processed = np.zeros_like(anomalies)
        in_anomaly = False
        anomaly_start = 0
        
        for i in range(len(anomalies)):
            if anomalies[i] and not in_anomaly:
                in_anomaly = True
                anomaly_start = i
            elif not anomalies[i] and in_anomaly:
                in_anomaly = False
                # 只保留持续时间达到阈值的异常
                if i - anomaly_start >= self.params['min_anomaly_duration']:
                    anomalies_processed[anomaly_start:i] = True
        
        # 处理最后一个异常段
        if in_anomaly and len(anomalies) - anomaly_start >= self.params['min_anomaly_duration']:
            anomalies_processed[anomaly_start:] = True
        
        # 保存结果到缓存
        if data_hash is not None:
            try:
                cache_path = self._get_cache_path(data_hash)
                np.savez(cache_path, anomalies=anomalies_processed, scores=combined_score)
                # print(f"[性能优化] 卡钻检测结果已缓存到: {cache_path}")
            except Exception as e:
                print(f"[性能优化] 缓存保存失败: {e}")
        
        print(f"[性能优化] 检测到异常比例: {np.mean(anomalies_processed)*100:.2f}%")
        return anomalies_processed, combined_score

    def _replace_nan_and_inf(self, array):
        """替换数组中的NaN和无穷值为插值"""
        array = np.array(array, dtype=np.float64)
        mask = np.isnan(array) | np.isinf(array)

        if np.all(mask):
            # 全是NaN或Inf，用0替换
            return np.zeros_like(array)

        if np.any(mask):
            # 有部分NaN或Inf
            indices = np.arange(len(array))
            valid_indices = indices[~mask]
            valid_values = array[~mask]

            # 使用前后有效值的平均替换
            for i in indices[mask]:
                # 找到前一个有效值
                prev_valid = valid_indices[valid_indices < i]
                prev_value = valid_values[len(prev_valid) - 1] if len(prev_valid) > 0 else valid_values[0]

                # 找到后一个有效值
                next_valid = valid_indices[valid_indices > i]
                next_value = valid_values[len(valid_values) - len(next_valid)] if len(next_valid) > 0 else valid_values[
                    -1]

                # 用平均值替换
                array[i] = (prev_value + next_value) / 2

        return array

    def tune_parameters(self, data, torque_col_idx=7, hook_load_col_idx=5, target_anomaly_ratio=0.03):
        """
        自动调整检测参数

        参数:
            data: 时间序列数据
            torque_col_idx: 扭矩列索引
            hook_load_col_idx: 大钩负荷列索引
            target_anomaly_ratio: 目标异常比例

        返回:
            优化后的参数
        """
        # 基础参数调整范围
        param_ranges = {
            'score_threshold': [0.5, 0.6, 0.7, 0.8, 0.9],
            'min_anomaly_duration': [3, 5, 7, 10],
            'torque_threshold': [1.5, 2.0, 2.5, 3.0],
            'hook_load_threshold': [1.5, 2.0, 2.5, 3.0]
        }

        best_params = self.params.copy()
        best_ratio_diff = float('inf')

        for score_threshold in param_ranges['score_threshold']:
            for min_duration in param_ranges['min_anomaly_duration']:
                for torque_th in param_ranges['torque_threshold']:
                    for hook_th in param_ranges['hook_load_threshold']:
                        # 更新参数
                        test_params = self.params.copy()
                        test_params['score_threshold'] = score_threshold
                        test_params['min_anomaly_duration'] = min_duration
                        test_params['torque_threshold'] = torque_th
                        test_params['hook_load_threshold'] = hook_th

                        # 临时更新检测器参数
                        old_params = self.params.copy()
                        self.params = test_params

                        # 运行检测
                        anomalies, _ = self.detect(data, torque_col_idx, hook_load_col_idx)

                        # 恢复原参数
                        self.params = old_params

                        # 计算检测异常比例
                        anomaly_ratio = np.mean(anomalies)
                        ratio_diff = abs(anomaly_ratio - target_anomaly_ratio)

                        # 如果更接近目标比例，则更新最佳参数
                        if ratio_diff < best_ratio_diff:
                            best_ratio_diff = ratio_diff
                            best_params = test_params
                            # print(f"找到更好的参数: 阈值={score_threshold}, 持续时间={min_duration}, 扭矩阈值={torque_th}, 大钩阈值={hook_th}")
                            # print(f"异常比例: {anomaly_ratio * 100:.3f}%, 与目标差异: {ratio_diff * 100:.3f}%")

        print(f"最终参数: {best_params}")
        print(f"最佳异常比例差异: {best_ratio_diff * 100:.3f}%")

        # 更新检测器参数
        self.params = best_params
        return best_params