# 卡钻检测算法对接文档

## 一、系统概述

本系统是基于深度学习的时间序列分析算法，专门用于钻井过程中的卡钻检测。系统能够接收实时钻井参数数据，分析时间序列特征，并输出卡钻预警结果。

## 二、环境要求

- Python 3.7 或以上版本
- PyTorch 1.8 或以上版本
- CUDA 支持（可选，用于GPU加速）
- 依赖库：numpy, pandas, sklearn, matplotlib

## 三、数据接口规范

### 1. 输入数据格式

#### 1.1 训练数据
- 文件格式：`.npy`或`.csv`
- 数据结构：二维数组，每行为一个时间点的多个参数，最后一列为时间戳
- 必要字段：钻井参数（扭矩、钻压、钻速等）+ 时间戳

#### 1.2 测试/预测数据
- 文件格式：`.npy`或`.csv`
- 数据结构：与训练数据相同
- 时间戳格式：支持多种格式，包括标准时间字符串、时间戳数值等

### 2. 输出数据格式

- 异常检测结果：二进制数组，每个元素对应一个时间窗口的检测结果（0=正常，1=卡钻）
- 异常分数：浮点型数组，数值越大表示异常可能性越高
- 汇总报告：包含检测结果统计、异常比例等信息

## 四、算法参数配置

主要参数包括：

```
{
  "task_name": "anomaly_detection",  // 任务类型，固定为异常检测
  "is_training": 0,                  // 0表示预测模式，1表示训练模式
  "model_id": "kazuan_detector",     // 模型标识
  "model": "TimesNet",               // 使用的模型类型
  
  // 数据相关参数
  "data": "anomaly_detection",       // 数据类型标识
  "root_path": "./dataset/",         // 数据根目录
  "test_file": "test_data.npy",      // 测试数据文件名
  "label_file": "test_data_label.npy", // 标签文件名（可选）
  "no_label": false,                 // 是否无标签模式
  
  // 模型参数
  "seq_len": 96,                     // 序列长度（时间窗口大小）
  "enc_in": 12,                      // 输入特征维度（参数数量）
  "d_model": 128,                    // 模型隐层维度
  "anomaly_ratio": 1                 // 异常比例阈值
}
```

## 五、API接口说明

### 1. 模型预测接口

命令行方式调用：

```bash
python run.py --task_name anomaly_detection --is_training 0 --model_id kazuan_detector --model TimesNet --data anomaly_detection --root_path ./dataset/ --test_file 测试数据.npy --no_label
```

### 2. 集成API接口（AI写的）

```python
# 初始化模型并加载检测器
from exp.exp_anomaly_detection import Exp_Anomaly_Detection
import argparse

# 创建参数对象
args = argparse.Namespace(
    task_name="anomaly_detection",
    is_training=0,
    model_id="kazuan_detector",
    model="TimesNet",
    data="anomaly_detection",
    root_path="./dataset/",
    test_file="测试数据.npy",
    no_label=True,
    seq_len=96,
    enc_in=12,
    d_model=128
)

# 创建实验对象
exp = Exp_Anomaly_Detection(args)

# 执行预测
result = exp.test("预测设置名称", test=1)
```

## 六、注意事项

1. 数据格式必须严格遵循规范，特别是时间戳列必须放在最后一列
2. 参数数量必须与训练模型时使用的一致
3. 时间窗口大小（seq_len）对检测精度有重要影响
4. 预测结果的保存路径默认在./results/目录下

